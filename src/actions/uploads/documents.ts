"use server";

import { uploadToS3, deleteFromS3 } from "@/lib/s3";
import { auth } from "@/lib/auth";

export async function uploadPurchaseDocument(formData: FormData) {
  console.log("[uploadPurchaseDocument] Starting upload process");

  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    console.log("[uploadPurchaseDocument] User not authenticated");
    return { error: "Tidak terautentikasi!" };
  }

  const userId = user.id;
  const file = formData.get("file") as File;
  console.log(
    `[uploadPurchaseDocument] File received: ${file?.name}, Size: ${file?.size} bytes`
  );

  if (!file) {
    console.log("[uploadPurchaseDocument] No file provided");
    return { error: "No file provided" };
  }

  // Validate file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB in bytes
  if (file.size > maxSize) {
    console.log(
      `[uploadPurchaseDocument] File too large: ${file.size} bytes (max: ${maxSize})`
    );
    return { error: "Ukuran file maksimal 10MB", success: false };
  }

  try {
    // Upload the file to AWS S3 with organized folder structure
    const result = await uploadToS3(file, userId, "purchases/lampiran");

    if (result.success && result.url) {
      return { url: result.url, success: true, filename: file.name };
    } else {
      return {
        error: result.error || "Failed to upload document",
        success: false,
      };
    }
  } catch (error) {
    console.error("Error uploading to Blob:", error);
    return { error: "Failed to upload document", success: false };
  }
}

export async function uploadSalesDocument(formData: FormData) {
  console.log("[uploadSalesDocument] Starting upload process");

  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    console.log("[uploadSalesDocument] User not authenticated");
    return { error: "Tidak terautentikasi!" };
  }

  const userId = user.id;
  const file = formData.get("file") as File;
  console.log(
    `[uploadSalesDocument] File received: ${file?.name}, Size: ${file?.size} bytes`
  );

  if (!file) {
    console.log("[uploadSalesDocument] No file provided");
    return { error: "No file provided" };
  }

  // Validate file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB in bytes
  if (file.size > maxSize) {
    console.log(
      `[uploadSalesDocument] File too large: ${file.size} bytes (max: ${maxSize})`
    );
    return { error: "Ukuran file maksimal 10MB", success: false };
  }

  try {
    // Upload the file to AWS S3 with organized folder structure
    const result = await uploadToS3(file, userId, "sales/lampiran");

    if (result.success && result.url) {
      return { url: result.url, success: true, filename: file.name };
    } else {
      return {
        error: result.error || "Failed to upload document",
        success: false,
      };
    }
  } catch (error) {
    console.error("Error uploading to Blob:", error);
    return { error: "Failed to upload document", success: false };
  }
}

export async function deletePurchaseDocument(url: string) {
  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return { error: "Tidak terautentikasi!" };
  }

  if (!url) {
    return { error: "No document URL provided" };
  }

  try {
    // Delete the file from AWS S3
    const result = await deleteFromS3(url);

    if (result.success) {
      return { success: true, message: "Dokumen berhasil dihapus" };
    } else {
      return {
        error: result.error || "Failed to delete document",
        success: false,
      };
    }
  } catch (error) {
    console.error("Error deleting from Blob:", error);
    return { error: "Failed to delete document", success: false };
  }
}

export async function deleteSalesDocument(url: string) {
  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return { error: "Tidak terautentikasi!" };
  }

  if (!url) {
    return { error: "No document URL provided" };
  }

  try {
    // Delete the file from AWS S3
    const result = await deleteFromS3(url);

    if (result.success) {
      return { success: true, message: "Dokumen berhasil dihapus" };
    } else {
      return {
        error: result.error || "Failed to delete document",
        success: false,
      };
    }
  } catch (error) {
    console.error("Error deleting from Blob:", error);
    return { error: "Failed to delete document", success: false };
  }
}
