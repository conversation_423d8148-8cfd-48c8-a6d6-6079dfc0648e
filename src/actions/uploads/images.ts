"use server";

import { uploadToS3, deleteFromS3 } from "@/lib/s3";
import { auth } from "@/lib/auth";

export async function uploadProductImage(formData: FormData) {
  console.log("[uploadProductImage] Starting upload process");

  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    console.log("[uploadProductImage] User not authenticated");
    return { error: "Tidak terautentikasi!" };
  }

  const userId = user.id;
  const file = formData.get("file") as File;
  console.log(
    `[uploadProductImage] File received: ${file?.name}, Size: ${file?.size} bytes`
  );

  if (!file) {
    console.log("[uploadProductImage] No file provided");
    return { error: "No file provided" };
  }

  try {
    // Upload the file to AWS S3 with organized folder structure
    const result = await uploadToS3(file, userId, "products");

    if (result.success && result.url) {
      return { url: result.url, success: true };
    } else {
      return {
        error: result.error || "Failed to upload image",
        success: false,
      };
    }
  } catch (error) {
    console.error("Error uploading to Blob:", error);
    return { error: "Failed to upload image", success: false };
  }
}

export async function deleteProductImage(imageUrl: string) {
  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return { error: "Tidak terautentikasi!" };
  }

  if (!imageUrl) {
    return { error: "No image URL provided" };
  }

  try {
    // Delete the file from AWS S3
    const result = await deleteFromS3(imageUrl);

    if (result.success) {
      return { success: true, message: "Gambar berhasil dihapus" };
    } else {
      return {
        error: result.error || "Failed to delete image",
        success: false,
      };
    }
  } catch (error) {
    console.error("Error deleting from Blob:", error);
    return { error: "Failed to delete image", success: false };
  }
}
