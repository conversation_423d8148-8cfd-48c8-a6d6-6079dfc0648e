"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { getDateRange } from "./_utils";
import { startOfDay, endOfDay } from "date-fns";

// Function to get services report data for dashboard (simplified version)
export const getServicesReportData = async (dateRange: string) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    const services = await db.service.findMany({
      where: {
        userId: effectiveUserId,
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        receivedDate: "desc",
      },
    });

    const servicesData = services.map((service) => ({
      id: service.id,
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      deviceType: service.deviceType,
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      status: service.status,
      estimatedCost: service.estimatedCost
        ? service.estimatedCost.toNumber()
        : 0,
      finalCost: service.finalCost ? service.finalCost.toNumber() : 0,
      receivedDate: service.receivedDate.toISOString(),
      completedDate: service.completedDate
        ? service.completedDate.toISOString()
        : null,
      // Keep legacy fields for backward compatibility
      total: service.finalCost
        ? service.finalCost.toNumber()
        : service.estimatedCost
          ? service.estimatedCost.toNumber()
          : 0,
      date: service.receivedDate.toISOString(),
    }));

    return {
      success: true,
      data: servicesData,
    };
  } catch (error) {
    console.error("Error fetching services report data:", error);
    return {
      error: "Gagal mengambil data laporan servis.",
    };
  }
};

// Function to get service report data with optional date filtering
export const getServiceReportData = async (filters?: {
  reportType?: "harian" | "bulanan" | "tahunan";
  selectedDate?: Date;
  selectedMonth?: number;
  selectedYear?: number;
}) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Build where clause with date filtering if provided
    const whereClause: any = {
      userId: effectiveUserId,
    };

    // Add date filtering based on report type and selected dates
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (filters) {
      if (filters.reportType === "harian" && filters.selectedDate) {
        startDate = startOfDay(filters.selectedDate);
        endDate = endOfDay(filters.selectedDate);
      } else if (
        filters.reportType === "bulanan" &&
        filters.selectedMonth !== undefined &&
        filters.selectedYear
      ) {
        startDate = new Date(filters.selectedYear, filters.selectedMonth, 1);
        endDate = endOfDay(
          new Date(filters.selectedYear, filters.selectedMonth + 1, 0)
        );
      } else if (filters.reportType === "tahunan" && filters.selectedYear) {
        startDate = new Date(filters.selectedYear, 0, 1);
        endDate = endOfDay(new Date(filters.selectedYear, 11, 31));
      }

      if (startDate && endDate) {
        whereClause.receivedDate = {
          gte: startDate,
          lte: endDate,
        };
      }
    }

    console.log("Service report query filters:", {
      reportType: filters?.reportType,
      selectedYear: filters?.selectedYear,
      whereClause,
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString(),
    });

    // First check total services for this user
    const totalServices = await db.service.count({
      where: { userId: effectiveUserId },
    });
    console.log(`Total services in database for user: ${totalServices}`);

    const services = await db.service.findMany({
      where: whereClause,
      include: {
        serviceHistory: true,
        spareParts: true, // Include spare parts
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log(
      `Found ${services.length} services in database for report with filters`
    );

    const serviceData = services.map((service) => ({
      id: service.id,
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || "-",
      deviceType: service.deviceType,
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || "-",
      problemDescription: service.problemDescription,
      diagnosisNotes: service.diagnosisNotes || "-",
      repairNotes: service.repairNotes || "-",
      estimatedCost: service.estimatedCost
        ? service.estimatedCost.toNumber()
        : 0,
      finalCost: service.finalCost ? service.finalCost.toNumber() : 0,
      warrantyPeriod: service.warrantyPeriod || 0,
      status: service.status,
      receivedDate: service.receivedDate.toISOString(),
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.toISOString()
        : "-",
      completedDate: service.completedDate
        ? service.completedDate.toISOString()
        : "-",
      deliveredDate: service.deliveredDate
        ? service.deliveredDate.toISOString()
        : "-",
      createdAt: service.createdAt.toISOString(),
      updatedAt: service.updatedAt.toISOString(),
      isDraft: service.isDraft,
      lampiran: service.lampiran as { url: string; filename: string }[],
      spareParts: service.spareParts.map((part) => ({
        name: part.name,
        barcode: part.barcode || "-",
        quantity: part.quantity,
      })),
    }));

    const totalSpareParts = services.reduce(
      (sum, service) => sum + service.spareParts.length,
      0
    );

    return {
      success: true,
      data: serviceData,
      summary: {
        totalServices: serviceData.length,
        totalSpareParts: totalSpareParts,
      },
    };
  } catch (error) {
    console.error("Error fetching service report data:", error);
    return { error: "Gagal mengambil data servis" };
  }
};
