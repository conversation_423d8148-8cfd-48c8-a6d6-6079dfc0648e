"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { generateSupplierId, generatePurchaseId } from "@/lib/generate-id";
import { createSystemNotification } from "@/lib/create-system-notification";
import * as XLSX from "xlsx";

interface ImportSummary {
  purchasesCreated: number;
  suppliersCreated: number;
  warehousesCreated: number;
  errors: string[];
}

interface ImportResult {
  success?: string;
  error?: string;
  summary: ImportSummary;
}

// Utility function to sanitize string inputs
const sanitizeString = (value: any): string => {
  if (value === null || value === undefined || value === "") return "";
  return String(value).trim().substring(0, 255);
};

// Utility function to sanitize number inputs
const sanitizeNumber = (value: any): number => {
  if (value === null || value === undefined || value === "") return 0;
  const num = Number(value);
  return isNaN(num) ? 0 : Math.max(0, num);
};

// Utility function to sanitize date inputs
const sanitizeDate = (value: any): Date => {
  if (value === null || value === undefined || value === "") {
    return new Date();
  }

  // Handle Excel date serial numbers
  if (typeof value === "number") {
    // Excel date serial number (days since 1900-01-01)
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(
      excelEpoch.getTime() + (value - 1) * 24 * 60 * 60 * 1000
    );
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Handle string dates
  if (typeof value === "string") {
    const date = new Date(value);
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Handle Date objects
  if (value instanceof Date) {
    return isNaN(value.getTime()) ? new Date() : value;
  }

  return new Date();
};

// Main import function for purchases
export const importPurchases = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  try {
    console.log("[IMPORT] Starting purchase import process");

    // Get effective user ID
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return {
        error: "Tidak terautentikasi",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["User tidak terautentikasi"],
        },
      };
    }

    // Parse Excel file
    const workbook = XLSX.read(arrayBuffer, { type: "array" });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`[IMPORT] Parsed ${data.length} rows from Excel`);

    if (data.length < 2) {
      return {
        error: "File Excel kosong atau tidak memiliki data",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["File tidak memiliki data yang valid"],
        },
      };
    }

    // Get headers from first row
    const headers = data[0] as string[];
    console.log(`[IMPORT] Headers found in Excel:`, headers);
    const dataRows = data.slice(1);

    // Convert to objects
    const purchaseData = (dataRows as any[][])
      .map((row: any[], index: number) => {
        const obj: any = {};
        headers.forEach((header, i) => {
          obj[header] = row[i];
        });
        obj._rowIndex = index + 2; // +2 because we start from row 2 (after header)
        return obj;
      })
      .filter((row) => {
        // Filter out empty rows
        const tanggalPembelian = sanitizeString(row["Tanggal Pembelian"]);
        const namaProduk = sanitizeString(row["Nama Produk"]);
        const quantity = sanitizeNumber(row["Quantity"]);
        const hargaBeli = sanitizeNumber(row["Harga Beli"]);

        console.log(
          `[IMPORT] Row validation - Tanggal: "${tanggalPembelian}", Produk: "${namaProduk}", Qty: ${quantity}, Harga: ${hargaBeli}`
        );

        return tanggalPembelian && namaProduk && quantity > 0 && hargaBeli > 0;
      });

    console.log(
      `[IMPORT] Filtered to ${purchaseData.length} valid purchase rows`
    );

    if (purchaseData.length === 0) {
      return {
        error: "Tidak ada data pembelian yang valid ditemukan",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Tidak ada baris data yang memenuhi kriteria minimum"],
        },
      };
    }

    // Pre-validate all products before processing any rows
    console.log("[IMPORT] Validating product availability...");
    const uniqueProductNames = new Set<string>();

    // Collect all unique product names from the import data
    purchaseData.forEach((row) => {
      const productName = sanitizeString(row["Nama Produk"]);
      if (productName) {
        uniqueProductNames.add(productName);
      }
    });

    // Check if all products exist in the database
    const existingProducts = await db.product.findMany({
      where: {
        name: {
          in: Array.from(uniqueProductNames),
        },
        userId: effectiveUserId,
      },
      select: {
        name: true,
      },
    });

    const existingProductNames = new Set(existingProducts.map((p) => p.name));
    const missingProducts: string[] = [];

    uniqueProductNames.forEach((productName) => {
      if (!existingProductNames.has(productName)) {
        missingProducts.push(productName);
      }
    });

    if (missingProducts.length > 0) {
      const errorMessage = `Produk berikut tidak ditemukan di sistem: ${missingProducts.join(", ")}. Silakan buat produk tersebut terlebih dahulu atau hapus dari file import.`;

      // Create notification for missing products
      try {
        await createSystemNotification(
          "error",
          "Import Pembelian Gagal - Produk Tidak Ditemukan",
          `Import pembelian dibatalkan karena ${missingProducts.length} produk tidak ditemukan di sistem. ${errorMessage}`,
          false
        );
      } catch (notificationError) {
        console.error(
          "Failed to create validation notification:",
          notificationError
        );
      }

      return {
        error: errorMessage,
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: [errorMessage],
        },
      };
    }

    console.log(
      `[IMPORT] Product validation passed - all ${uniqueProductNames.size} products found`
    );

    if (purchaseData.length > 500) {
      return {
        error: "Terlalu banyak data. Maksimal 500 transaksi per import",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Jumlah data melebihi batas maksimal 500 transaksi"],
        },
      };
    }

    // Process import in smaller batches
    const BATCH_SIZE = 10;
    let totalPurchasesCreated = 0;
    let totalSuppliersCreated = 0;
    let totalWarehousesCreated = 0;
    const allErrors: string[] = [];

    // Process data in batches
    for (
      let batchStart = 0;
      batchStart < purchaseData.length;
      batchStart += BATCH_SIZE
    ) {
      const batchEnd = Math.min(batchStart + BATCH_SIZE, purchaseData.length);
      const batch = purchaseData.slice(batchStart, batchEnd);

      console.log(
        `[IMPORT] Processing batch ${Math.floor(batchStart / BATCH_SIZE) + 1}/${Math.ceil(purchaseData.length / BATCH_SIZE)} (${batch.length} purchases)`
      );

      // Process each row individually to avoid transaction rollback issues
      let batchPurchasesCreated = 0;
      let batchSuppliersCreated = 0;
      let batchWarehousesCreated = 0;
      const batchErrors: string[] = [];

      for (const row of batch) {
        try {
          const rowResult = await db.$transaction(
            async (tx) => {
              // Validate and sanitize required fields
              const purchaseDate = sanitizeDate(row["Tanggal Pembelian"]);
              const productName = sanitizeString(row["Nama Produk"]);
              const quantity = sanitizeNumber(row["Quantity"]);
              const costPrice = sanitizeNumber(row["Harga Beli"]);

              if (!productName) {
                throw new Error("Nama Produk tidak boleh kosong");
              }

              if (quantity <= 0) {
                throw new Error("Quantity harus lebih dari 0");
              }

              if (costPrice <= 0) {
                throw new Error("Harga Beli harus lebih dari 0");
              }

              // Find existing product
              const existingProduct = await tx.product.findFirst({
                where: {
                  name: productName,
                  userId: effectiveUserId,
                },
              });

              if (!existingProduct) {
                throw new Error(
                  `Produk "${productName}" tidak ditemukan di sistem`
                );
              }

              // Handle supplier
              let supplierId: string | null = null;
              let suppliersCreated = 0;
              const supplierName = sanitizeString(row["Supplier"]);
              if (supplierName) {
                let supplier = await tx.supplier.findFirst({
                  where: {
                    name: supplierName,
                    userId: effectiveUserId,
                  },
                });

                if (!supplier) {
                  try {
                    const newSupplierId = await generateSupplierId();
                    supplier = await tx.supplier.create({
                      data: {
                        id: newSupplierId,
                        name: supplierName,
                        userId: effectiveUserId,
                      },
                    });
                    suppliersCreated++;
                  } catch (supplierError) {
                    // If supplier creation fails, try to find if it was created by another concurrent process
                    supplier = await tx.supplier.findFirst({
                      where: {
                        name: supplierName,
                        userId: effectiveUserId,
                      },
                    });

                    if (!supplier) {
                      // If still not found, throw the original error
                      throw supplierError;
                    }
                  }
                }
                supplierId = supplier.id;
              }

              // Handle warehouse
              let warehouseId: string | null = null;
              let warehousesCreated = 0;
              const warehouseName = sanitizeString(row["Gudang"]);
              if (warehouseName) {
                let warehouse = await tx.warehouse.findFirst({
                  where: {
                    name: warehouseName,
                    userId: effectiveUserId,
                  },
                });

                if (!warehouse) {
                  try {
                    warehouse = await tx.warehouse.create({
                      data: {
                        name: warehouseName,
                        userId: effectiveUserId,
                      },
                    });
                    warehousesCreated++;
                  } catch (warehouseError) {
                    // If warehouse creation fails, try to find if it was created by another concurrent process
                    warehouse = await tx.warehouse.findFirst({
                      where: {
                        name: warehouseName,
                        userId: effectiveUserId,
                      },
                    });

                    if (!warehouse) {
                      // If still not found, throw the original error
                      throw warehouseError;
                    }
                  }
                }
                warehouseId = warehouse.id;
              }

              // Calculate discounts and totals
              const discountPercentage = sanitizeNumber(row["Diskon (%)"]);
              const discountAmount = sanitizeNumber(row["Diskon (Rp)"]);
              const taxPercentage = sanitizeNumber(row["PPN (%)"]);

              const subtotal = quantity * costPrice;
              const finalDiscountAmount =
                discountAmount > 0
                  ? discountAmount
                  : (subtotal * discountPercentage) / 100;
              const afterDiscount = subtotal - finalDiscountAmount;
              const taxAmount = (afterDiscount * taxPercentage) / 100;
              const totalAmount = afterDiscount + taxAmount;

              // Generate transaction number with retry logic for concurrent imports
              const currentYear = new Date().getFullYear().toString().slice(-2);

              let transactionNumber: string = "";
              let purchaseId: string = "";
              let retryCount = 0;
              const maxRetries = 5;

              while (retryCount < maxRetries) {
                try {
                  // Get the latest transaction number
                  const lastPurchase = await tx.purchase.findFirst({
                    where: {
                      userId: effectiveUserId,
                      transactionNumber: {
                        startsWith: `BELI-${currentYear}B`,
                      },
                    },
                    orderBy: { transactionNumber: "desc" },
                  });

                  let nextNumber = 1;
                  if (lastPurchase?.transactionNumber) {
                    const match =
                      lastPurchase.transactionNumber.match(
                        /BELI-\d{2}B(\d{6})/
                      );
                    if (match) {
                      nextNumber = parseInt(match[1]) + 1 + retryCount; // Add retry count to avoid conflicts
                    }
                  }

                  transactionNumber = `BELI-${currentYear}B${nextNumber.toString().padStart(6, "0")}`;

                  // Generate purchase ID
                  purchaseId = await generatePurchaseId(effectiveUserId);

                  // Check if transaction number already exists
                  const existingTransaction = await tx.purchase.findFirst({
                    where: {
                      transactionNumber,
                      userId: effectiveUserId,
                    },
                  });

                  if (!existingTransaction) {
                    break; // Success, exit retry loop
                  }

                  retryCount++;
                } catch (error) {
                  retryCount++;
                  if (retryCount >= maxRetries) {
                    throw error;
                  }
                }
              }

              if (retryCount >= maxRetries) {
                throw new Error(
                  "Gagal menghasilkan nomor transaksi unik setelah beberapa percobaan"
                );
              }

              // Validate that we have valid IDs before proceeding
              if (!transactionNumber || !purchaseId) {
                throw new Error(
                  "Gagal menghasilkan ID transaksi atau nomor transaksi"
                );
              }

              // Parse status field
              const statusValue = sanitizeString(row["Status Pembayaran"]);
              const status = statusValue === "Lunas" ? "LUNAS" : "BELUM_LUNAS";

              // Create purchase
              const newPurchase = await tx.purchase.create({
                data: {
                  id: purchaseId,
                  purchaseDate,
                  totalAmount,
                  transactionNumber,
                  invoiceRef: sanitizeString(row["No. Invoice"]) || null,
                  memo: sanitizeString(row["Memo"]) || null,
                  status: status as any, // Cast to match Prisma enum
                  userId: effectiveUserId,
                  supplierId,
                  warehouseId,
                  isDraft: false,
                },
              });

              // Create purchase item
              await tx.purchaseItem.create({
                data: {
                  quantity,
                  costAtPurchase: costPrice,
                  unit: sanitizeString(row["Satuan"]) || "Pcs",
                  discountPercentage:
                    discountPercentage > 0 ? discountPercentage : null,
                  discountAmount:
                    finalDiscountAmount > 0 ? finalDiscountAmount : null,
                  tax: taxPercentage > 0 ? `${taxPercentage}%` : null,
                  purchaseId: newPurchase.id,
                  productId: existingProduct.id,
                },
              });

              // Update product stock
              await tx.product.update({
                where: { id: existingProduct.id },
                data: {
                  stock: {
                    increment: quantity,
                  },
                },
              });

              return {
                purchasesCreated: 1,
                suppliersCreated,
                warehousesCreated,
              };
            },
            { timeout: 30000 }
          );

          // Accumulate successful results
          batchPurchasesCreated += rowResult.purchasesCreated;
          batchSuppliersCreated += rowResult.suppliersCreated;
          batchWarehousesCreated += rowResult.warehousesCreated;
        } catch (error) {
          console.error(
            `[IMPORT] Error processing row ${row._rowIndex}:`,
            error
          );

          // Provide more specific error messages
          let errorMessage = "Error tidak diketahui";
          if (error instanceof Error) {
            if (error.message.includes("Unique constraint failed")) {
              if (error.message.includes("supplier")) {
                errorMessage = "Supplier dengan data yang sama sudah ada";
              } else if (error.message.includes("warehouse")) {
                errorMessage = "Gudang dengan nama yang sama sudah ada";
              } else if (error.message.includes("transactionNumber")) {
                errorMessage = "Nomor transaksi sudah ada";
              } else {
                errorMessage = "Data duplikat ditemukan";
              }
            } else if (
              error.message.includes("Produk") &&
              error.message.includes("tidak ditemukan")
            ) {
              errorMessage = error.message;
            } else if (error.message.includes("Gagal menghasilkan")) {
              errorMessage = error.message;
            } else {
              errorMessage = error.message;
            }
          }

          batchErrors.push(`Baris ${row._rowIndex}: ${errorMessage}`);
        }
      }

      // Accumulate batch results
      totalPurchasesCreated += batchPurchasesCreated;
      totalSuppliersCreated += batchSuppliersCreated;
      totalWarehousesCreated += batchWarehousesCreated;
      allErrors.push(...batchErrors);

      console.log(
        `[IMPORT] Batch completed: ${batchPurchasesCreated} purchases created`
      );
    }

    // Return final results
    const finalResult = {
      purchasesCreated: totalPurchasesCreated,
      suppliersCreated: totalSuppliersCreated,
      warehousesCreated: totalWarehousesCreated,
      errors: allErrors,
    };

    // Create notifications based on import results
    try {
      if (allErrors.length > 0 && totalPurchasesCreated === 0) {
        // Complete failure notification
        await createSystemNotification(
          "error",
          "Import Pembelian Gagal",
          `Import pembelian gagal sepenuhnya. ${allErrors.length} error ditemukan. Silakan periksa file dan coba lagi.`,
          false // Don't send email for import failures
        );

        return {
          error: "Import gagal",
          summary: finalResult,
        };
      } else if (allErrors.length > 0 && totalPurchasesCreated > 0) {
        // Partial success notification
        await createSystemNotification(
          "warning",
          "Import Pembelian Sebagian Berhasil",
          `Import pembelian selesai dengan ${totalPurchasesCreated} pembelian berhasil diimpor, namun ${allErrors.length} baris gagal diproses. ${totalSuppliersCreated} supplier baru dan ${totalWarehousesCreated} gudang baru telah dibuat.`,
          false
        );
      } else {
        // Complete success notification
        await createSystemNotification(
          "success",
          "Import Pembelian Berhasil",
          `Import pembelian berhasil! ${totalPurchasesCreated} pembelian berhasil diimpor. ${totalSuppliersCreated} supplier baru dan ${totalWarehousesCreated} gudang baru telah dibuat.`,
          false
        );
      }
    } catch (notificationError) {
      console.error("Failed to create import notification:", notificationError);
      // Don't fail the import if notification creation fails
    }

    return {
      success: `Import berhasil! ${totalPurchasesCreated} pembelian berhasil diimpor.`,
      summary: finalResult,
    };
  } catch (error) {
    console.error("Import error:", error);

    // Create error notification
    try {
      await createSystemNotification(
        "error",
        "Import Pembelian Error",
        `Terjadi kesalahan sistem saat mengimpor pembelian: ${error instanceof Error ? error.message : "Unknown error"}`,
        false
      );
    } catch (notificationError) {
      console.error("Failed to create error notification:", notificationError);
    }

    return {
      error: "Gagal memproses file import",
      summary: {
        purchasesCreated: 0,
        suppliersCreated: 0,
        warehousesCreated: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
      },
    };
  }
};
