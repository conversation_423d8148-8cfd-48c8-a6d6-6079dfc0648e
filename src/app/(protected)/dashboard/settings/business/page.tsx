import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import React from "react";
import BusinessSettings from "@/components/pages/dashboard/settings/business/business-settings";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";
import { db } from "@/lib/prisma"; // Import db
import { getSubscriptionInfo } from "@/actions/users/subscription-info"; // Import getSubscriptionInfo

const BusinessSettingsPage = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch user data directly
  const userData = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      id: true,
      name: true,
      username: true,
      email: true,
      image: true,
      phone: true,
      bio: true,
      birthday: true,
      role: true,
      lastLogin: true,
      createdAt: true,
      currentPlan: true,
      subscriptionExpiry: true,
      businessInfo: {
        select: {
          companyId: true,
          companyName: true,
          companyUsername: true,
        },
      },
    },
  });

  if (!userData) {
    console.error("User not found for ID:", session.user.id);
    redirect("/dashboard"); // Redirect if user not found
  }

  // Transform userData to match the User interface
  const user = {
    ...userData,
    companyId: userData.businessInfo?.companyId || null,
    companyName: userData.businessInfo?.companyName || null,
    companyUsername: userData.businessInfo?.companyUsername || null,
  };

  return (
    <DashboardLayout>
      <SettingsLayout>
        <BusinessSettings user={user} />
      </SettingsLayout>
    </DashboardLayout>
  );
};

export default BusinessSettingsPage;
