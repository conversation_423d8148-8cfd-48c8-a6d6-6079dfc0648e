import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SummariesPage from "@/components/pages/dashboard/summaries/summaries";
import { OnboardingGuard } from "@/components/auth/onboarding-guard";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Dashboard - Ringkasan | KivaPOS",
  description: "Ringkasan bisnis dan analitik Anda",
};

const Summaries = async () => {
  return (
    <OnboardingGuard requireOnboarding={true}>
      <DashboardLayout>
        <div className="py-6 px-1.5 sm:px-2 md:px-4">
          <SummariesPage />
        </div>
      </DashboardLayout>
    </OnboardingGuard>
  );
};

export default Summaries;
