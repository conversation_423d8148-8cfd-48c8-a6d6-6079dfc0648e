import React from "react";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import DashboardLayout from "@/components/layout/dashboardlayout";
import CustomerDetailPage from "@/components/pages/dashboard/customers/detail/customer-detail";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getCustomerById } from "@/actions/entities/customers";

export const metadata: Metadata = {
  title: "Detail Pelanggan | KivaPOS",
  description: "Detail informasi pelanggan",
};

type Props = {
  params: Promise<{ id: string }>;
};

// This is an async Server Component
export default async function CustomerDetail(props: Props) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const id = params.id;

  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch customer data
  const customerResult = await getCustomerById(id);

  if (customerResult.error || !customerResult.customer) {
    notFound();
  }

  return (
    <DashboardLayout>
      <CustomerDetailPage customer={customerResult.customer} />
    </DashboardLayout>
  );
}
