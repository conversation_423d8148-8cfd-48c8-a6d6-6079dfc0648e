"use client";

import * as React from "react";
import { format, getYear, getMonth } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { id } from "date-fns/locale";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DatePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  fromYear?: number;
  toYear?: number;
  // Add prop to control popover behavior when inside dialogs
  insideDialog?: boolean;
}

export function DatePicker({
  date,
  setDate,
  className,
  placeholder = "Pilih tanggal",
  disabled = false,
  fromYear = 1950,
  toYear = new Date().getFullYear(),
  insideDialog = false,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false);
  const [calendarDate, setCalendarDate] = React.useState<Date | undefined>(
    date || new Date()
  );
  const [year, setYear] = React.useState<number>(
    date ? getYear(date) : getYear(new Date())
  );
  const [month, setMonth] = React.useState<number>(
    date ? getMonth(date) : getMonth(new Date())
  );

  // Generate years for dropdown
  const years = React.useMemo(() => {
    const years = [];
    for (let i = toYear; i >= fromYear; i--) {
      years.push(i);
    }
    return years;
  }, [fromYear, toYear]);

  // Update calendar view when year or month changes
  React.useEffect(() => {
    if (calendarDate) {
      const newDate = new Date(calendarDate);
      newDate.setFullYear(year);
      newDate.setMonth(month);
      setCalendarDate(newDate);
    }
  }, [year, month]);

  // When the selected date prop changes, update the internal year/month state
  React.useEffect(() => {
    if (date) {
      setYear(getYear(date));
      setMonth(getMonth(date));
      setCalendarDate(date);
    }
  }, [date]);

  // Close popover when component unmounts or parent dialog closes
  React.useEffect(() => {
    return () => {
      setOpen(false);
    };
  }, []);

  // Handle date selection and close popover
  const handleDateSelect = (selectedDate: Date | undefined) => {
    setDate(selectedDate);
    setOpen(false); // Always close after selection
  };

  // Handle popover close
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };

  // Reset internal state when popover closes
  React.useEffect(() => {
    if (!open && date) {
      setYear(getYear(date));
      setMonth(getMonth(date));
      setCalendarDate(date);
    }
  }, [open, date]);

  return (
    <Popover
      open={open}
      onOpenChange={handleOpenChange}
      // Use modal={false} when inside dialogs to prevent focus trap conflicts
      modal={!insideDialog}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
          onClick={() => !disabled && setOpen(true)}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? (
            format(date, "d MMMM yyyy", { locale: id })
          ) : (
            <span>{placeholder}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-auto p-0 shadow-lg border rounded-lg bg-white dark:bg-slate-800"
        align="start"
        side="bottom"
        sideOffset={4}
        // Prevent event bubbling that might interfere with parent dialogs
        onPointerDownOutside={(e) => {
          // Don't close if clicking on select dropdowns
          const target = e.target as Element;
          if (
            target.closest('[role="listbox"]') ||
            target.closest("[data-radix-popper-content-wrapper]")
          ) {
            e.preventDefault();
            return;
          }
          setOpen(false);
        }}
        onEscapeKeyDown={() => setOpen(false)}
        onFocusOutside={(e) => {
          // Don't close if focusing on select elements
          const target = e.target as Element;
          if (
            target.closest('[role="listbox"]') ||
            target.closest("[data-radix-select-trigger]")
          ) {
            e.preventDefault();
            return;
          }
        }}
      >
        <div className="p-3 border-b border-border bg-slate-50 dark:bg-slate-700 flex justify-between items-center gap-2 rounded-t-lg">
          {/* Month Dropdown */}
          <Select
            value={month.toString()}
            onValueChange={(value) => setMonth(parseInt(value))}
          >
            <SelectTrigger className="w-[130px] h-8 text-xs bg-white dark:bg-slate-600">
              <SelectValue placeholder="Pilih Bulan" />
            </SelectTrigger>
            <SelectContent className="z-[60]">
              {[
                { value: "0", label: "Januari" },
                { value: "1", label: "Februari" },
                { value: "2", label: "Maret" },
                { value: "3", label: "April" },
                { value: "4", label: "Mei" },
                { value: "5", label: "Juni" },
                { value: "6", label: "Juli" },
                { value: "7", label: "Agustus" },
                { value: "8", label: "September" },
                { value: "9", label: "Oktober" },
                { value: "10", label: "November" },
                { value: "11", label: "Desember" },
              ].map((month) => (
                <SelectItem
                  key={month.value}
                  value={month.value}
                  className="text-xs"
                >
                  {month.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Year Dropdown */}
          <Select
            value={year.toString()}
            onValueChange={(value) => setYear(parseInt(value))}
          >
            <SelectTrigger className="w-[100px] h-8 text-xs bg-white dark:bg-slate-600">
              <SelectValue placeholder="Pilih Tahun" />
            </SelectTrigger>
            <SelectContent className="max-h-[200px] z-[60]">
              {years.map((year) => (
                <SelectItem
                  key={year}
                  value={year.toString()}
                  className="text-xs"
                >
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="p-0">
          <Calendar
            mode="single"
            selected={date}
            onSelect={handleDateSelect}
            month={calendarDate}
            onMonthChange={setCalendarDate}
            initialFocus={open}
            locale={id}
            className="rounded-b-lg"
            classNames={{
              months:
                "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
              month: "space-y-4",
              caption: "flex justify-center pt-1 relative items-center",
              caption_label: "text-sm font-medium",
              nav: "space-x-1 flex items-center",
              nav_button:
                "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex",
              head_cell:
                "text-slate-500 rounded-md w-9 font-normal text-[0.8rem] dark:text-slate-400",
              row: "flex w-full mt-2",
              cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-slate-100 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20 dark:[&:has([aria-selected])]:bg-slate-800",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-slate-100 hover:text-slate-900 rounded-md dark:hover:bg-slate-800 dark:hover:text-slate-50",
              day_selected:
                "bg-slate-900 text-slate-50 hover:bg-slate-900 hover:text-slate-50 focus:bg-slate-900 focus:text-slate-50 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50 dark:hover:text-slate-900 dark:focus:bg-slate-50 dark:focus:text-slate-900",
              day_today:
                "bg-slate-100 text-slate-900 dark:bg-slate-800 dark:text-slate-50",
              day_outside: "text-slate-500 opacity-50 dark:text-slate-400",
              day_disabled: "text-slate-500 opacity-50 dark:text-slate-400",
              day_range_middle:
                "aria-selected:bg-slate-100 aria-selected:text-slate-900 dark:aria-selected:bg-slate-800 dark:aria-selected:text-slate-50",
              day_hidden: "invisible",
            }}
          />
        </div>

        {/* Action buttons */}
        <div className="p-3 border-t border-border bg-slate-50 dark:bg-slate-700 flex justify-between gap-2 rounded-b-lg">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setDate(undefined);
              setOpen(false);
            }}
            className="text-xs h-7"
          >
            Clear
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setOpen(false)}
            className="text-xs h-7"
          >
            Cancel
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
