"use client";

import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@/lib/utils";

// Component Props Interfaces
interface TabsProps extends React.ComponentProps<typeof TabsPrimitive.Root> {}
interface TabsListProps
  extends React.ComponentProps<typeof TabsPrimitive.List> {}
interface TabsTriggerProps
  extends React.ComponentProps<typeof TabsPrimitive.Trigger> {}
interface TabsContentProps
  extends React.ComponentProps<typeof TabsPrimitive.Content> {}

// Tabs Root Component
const Tabs = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Root>,
  TabsProps
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Root
    ref={ref}
    data-slot="tabs"
    className={cn("flex flex-col gap-4", className)}
    {...props}
  />
));
Tabs.displayName = "Tabs";

// Tabs List Component
const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  TabsListProps
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    data-slot="tabs-list"
    className={cn(
      "inline-flex h-12 w-fit items-center justify-center rounded-xl bg-gray-100 p-1 shadow-sm",
      "dark:bg-gray-800 dark:text-gray-300",
      "transition-all duration-300",
      className
    )}
    {...props}
  />
));
TabsList.displayName = "TabsList";

// Tabs Trigger Component
const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  TabsTriggerProps
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    data-slot="tabs-trigger"
    className={cn(
      // Base styles
      "inline-flex h-full flex-1 items-center justify-center gap-2 rounded-lg border border-transparent px-4 py-2 text-sm font-semibold tracking-tight whitespace-nowrap",
      // Active state
      "data-[state=active]:bg-white data-[state=active]:text-gray-900",
      "data-[state=active]:border-b-2 data-[state=active]:border-blue-100",
      "data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white",
      // Hover state
      "hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
      // Focus state
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2",
      "focus-visible:ring-offset-white dark:focus-visible:ring-offset-gray-900",
      // Transitions
      "transition-all duration-200 ease-in-out",
      // Disabled state
      "disabled:cursor-not-allowed disabled:opacity-50",
      // SVG styles
      "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-5",
      className
    )}
    {...props}
  />
));
TabsTrigger.displayName = "TabsTrigger";

// Tabs Content Component
const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  TabsContentProps
>(({ className, value, ...props }, ref) => {
  // Calculate animation class based on tab value
  const animationClass = React.useMemo(() => {
    const index = parseInt(value, 10) || value.charCodeAt(0) || 0;
    const directions = [
      "slide-in-from-left-2",
      "slide-in-from-right-2",
      "slide-in-from-top-2",
      "slide-in-from-bottom-2",
    ];
    return directions[Math.abs(index) % directions.length];
  }, [value]);

  return (
    <TabsPrimitive.Content
      ref={ref}
      data-slot="tabs-content"
      className={cn(
        // Base styles
        "mt-4 flex-1 rounded-xl bg-white p-6 shadow-sm",
        "dark:bg-gray-800",
        // Animation
        "animate-in fade-in-10",
        animationClass,
        "duration-500 ease-[cubic-bezier(0.4,0,0.2,1)]",
        className
      )}
      value={value}
      {...props}
    />
  );
});
TabsContent.displayName = "TabsContent";

export { Tabs, TabsList, TabsTrigger, TabsContent };
