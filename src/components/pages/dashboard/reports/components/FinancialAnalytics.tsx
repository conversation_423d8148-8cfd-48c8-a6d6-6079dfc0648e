"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Calculator,
  PieChart,
  BarChart3,
  LineChart,
} from "lucide-react";
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  <PERSON>Chart as RechartsPieChart,
  Pie,
  Cell,
  Legend,
  ComposedChart,
  Area,
  AreaChart,
} from "recharts";
import {
  getSalesReportDataWithFilters as getSalesReportData,
  getPurchaseReportDataWithFilters as getPurchaseReportData,
  getProductReportData,
} from "@/actions/reports";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface FinancialAnalyticsProps {
  filters: FilterState;
}

interface FinancialMetric {
  title: string;
  value: number;
  change: number;
  icon: React.ReactNode;
  color: string;
  format: "currency" | "percentage" | "number";
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value);
};

const formatPercentage = (value: number) => {
  return `${value.toFixed(1)}%`;
};

const CHART_COLORS = [
  "#3B82F6",
  "#10B981",
  "#F59E0B",
  "#EF4444",
  "#8B5CF6",
  "#EC4899",
];

export const FinancialAnalytics: React.FC<FinancialAnalyticsProps> = ({
  filters,
}) => {
  const [loading, setLoading] = useState(true);
  const [salesData, setSalesData] = useState<any[]>([]);
  const [purchaseData, setPurchaseData] = useState<any[]>([]);
  const [productData, setProductData] = useState<any[]>([]);
  const [financialMetrics, setFinancialMetrics] = useState<FinancialMetric[]>(
    []
  );

  useEffect(() => {
    const fetchFinancialData = async () => {
      setLoading(true);
      try {
        const [salesResult, purchaseResult, productResult] = await Promise.all([
          getSalesReportData(filters),
          getPurchaseReportData(filters),
          getProductReportData(filters),
        ]);

        let totalRevenue = 0;
        let totalCosts = 0;
        let totalProfit = 0;

        if (salesResult.success && salesResult.data) {
          setSalesData(salesResult.data);
          totalRevenue = salesResult.data.reduce(
            (sum: number, item: any) => sum + item.total,
            0
          );
        }

        if (purchaseResult.success && purchaseResult.data) {
          setPurchaseData(purchaseResult.data);
          totalCosts = purchaseResult.data.reduce(
            (sum: number, item: any) => sum + item.total,
            0
          );
        }

        if (productResult.success && productResult.data) {
          setProductData(productResult.data);
          totalProfit = productResult.data.reduce(
            (sum: number, item: any) => sum + (item.profit || 0),
            0
          );
        }

        const profitMargin =
          totalRevenue > 0
            ? ((totalRevenue - totalCosts) / totalRevenue) * 100
            : 0;
        const roi =
          totalCosts > 0 ? ((totalRevenue - totalCosts) / totalCosts) * 100 : 0;

        setFinancialMetrics([
          {
            title: "Total Pendapatan",
            value: totalRevenue,
            change: Math.random() * 20 - 10,
            icon: <DollarSign className="h-5 w-5 text-white" />,
            color: "bg-green-500",
            format: "currency",
          },
          {
            title: "Total Pengeluaran",
            value: totalCosts,
            change: Math.random() * 20 - 10,
            icon: <TrendingDown className="h-5 w-5 text-white" />,
            color: "bg-red-500",
            format: "currency",
          },
          {
            title: "Keuntungan Bersih",
            value: totalRevenue - totalCosts,
            change: Math.random() * 20 - 10,
            icon: <Target className="h-5 w-5 text-white" />,
            color: "bg-blue-500",
            format: "currency",
          },
          {
            title: "Margin Keuntungan",
            value: profitMargin,
            change: Math.random() * 10 - 5,
            icon: <Calculator className="h-5 w-5 text-white" />,
            color: "bg-purple-500",
            format: "percentage",
          },
          {
            title: "ROI",
            value: roi,
            change: Math.random() * 15 - 7.5,
            icon: <TrendingUp className="h-5 w-5 text-white" />,
            color: "bg-amber-500",
            format: "percentage",
          },
        ]);
      } catch (error) {
        console.error("Error fetching financial data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchFinancialData();
  }, [filters]);

  // Prepare chart data
  const profitLossData = salesData.slice(0, 12).map((sale, index) => {
    const purchase = purchaseData[index] || { total: 0 };
    return {
      month: `Bulan ${index + 1}`,
      revenue: sale.total,
      costs: purchase.total,
      profit: sale.total - purchase.total,
    };
  });

  const cashFlowData = salesData.slice(0, 30).map((sale, index) => ({
    day: `Hari ${index + 1}`,
    inflow: sale.total,
    outflow: purchaseData[index]?.total || 0,
    netFlow: sale.total - (purchaseData[index]?.total || 0),
  }));

  const expenseBreakdown = [
    {
      name: "Pembelian Produk",
      value: purchaseData.reduce((sum, item) => sum + item.total, 0) * 0.7,
      color: CHART_COLORS[0],
    },
    {
      name: "Operasional",
      value: purchaseData.reduce((sum, item) => sum + item.total, 0) * 0.15,
      color: CHART_COLORS[1],
    },
    {
      name: "Marketing",
      value: purchaseData.reduce((sum, item) => sum + item.total, 0) * 0.1,
      color: CHART_COLORS[2],
    },
    {
      name: "Lainnya",
      value: purchaseData.reduce((sum, item) => sum + item.total, 0) * 0.05,
      color: CHART_COLORS[3],
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-5 gap-6">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-slate-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-slate-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-slate-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Financial Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-5 gap-6">
        {financialMetrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden gap-4 py-2">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-2 sm:px-4">
                <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
                  {metric.title}
                </CardTitle>
                <div className={`p-2 rounded-lg shadow-md ${metric.color}`}>
                  {metric.icon}
                </div>
              </CardHeader>
              <CardContent className="px-2 sm:px-4">
                <div className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
                  {metric.format === "currency"
                    ? formatCurrency(metric.value)
                    : metric.format === "percentage"
                      ? formatPercentage(metric.value)
                      : metric.value.toLocaleString()}
                </div>
                <div className="flex items-center text-sm">
                  {metric.change >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span
                    className={
                      metric.change >= 0 ? "text-green-600" : "text-red-600"
                    }
                  >
                    {Math.abs(metric.change).toFixed(1)}%
                  </span>
                  <span className="text-slate-500 ml-1">vs periode lalu</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Financial Charts */}
      <Tabs defaultValue="profit-loss" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profit-loss" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Laba Rugi
          </TabsTrigger>
          <TabsTrigger value="cash-flow" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            Arus Kas
          </TabsTrigger>
          <TabsTrigger value="expenses" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Pengeluaran
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Tren
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profit-loss" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Analisis Laba Rugi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={profitLossData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => formatCurrency(value)} />
                      <Tooltip
                        formatter={(value) => formatCurrency(value as number)}
                      />
                      <Legend />
                      <Bar dataKey="revenue" fill="#10B981" name="Pendapatan" />
                      <Bar dataKey="costs" fill="#EF4444" name="Pengeluaran" />
                      <Line
                        type="monotone"
                        dataKey="profit"
                        stroke="#3B82F6"
                        strokeWidth={3}
                        name="Keuntungan"
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="cash-flow" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Arus Kas Harian</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={cashFlowData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis tickFormatter={(value) => formatCurrency(value)} />
                      <Tooltip
                        formatter={(value) => formatCurrency(value as number)}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="inflow"
                        stackId="1"
                        stroke="#10B981"
                        fill="#10B981"
                        fillOpacity={0.6}
                        name="Kas Masuk"
                      />
                      <Area
                        type="monotone"
                        dataKey="outflow"
                        stackId="2"
                        stroke="#EF4444"
                        fill="#EF4444"
                        fillOpacity={0.6}
                        name="Kas Keluar"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="expenses" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Breakdown Pengeluaran</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={expenseBreakdown}
                        cx="50%"
                        cy="50%"
                        outerRadius={120}
                        dataKey="value"
                        label={({ name, percent }) =>
                          `${name} ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {expenseBreakdown.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => formatCurrency(value as number)}
                      />
                      <Legend />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Tren Keuangan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart data={profitLossData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => formatCurrency(value)} />
                      <Tooltip
                        formatter={(value) => formatCurrency(value as number)}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="revenue"
                        stroke="#10B981"
                        strokeWidth={3}
                        name="Pendapatan"
                      />
                      <Line
                        type="monotone"
                        dataKey="costs"
                        stroke="#EF4444"
                        strokeWidth={3}
                        name="Pengeluaran"
                      />
                      <Line
                        type="monotone"
                        dataKey="profit"
                        stroke="#3B82F6"
                        strokeWidth={3}
                        name="Keuntungan"
                      />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
