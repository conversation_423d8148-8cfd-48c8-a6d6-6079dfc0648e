"use client";

import React, { useState, useTransition } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { LoaderCircle } from "lucide-react";
import { TransactionPaymentStatus } from "../types";
import { updateSaleStatus } from "@/actions/entities/sales";

interface InlineStatusEditorProps {
  saleId: string;
  currentStatus: TransactionPaymentStatus;
  disabled?: boolean;
}

const getStatusDisplayText = (status: TransactionPaymentStatus): string => {
  switch (status) {
    case TransactionPaymentStatus.LUNAS:
      return "Lunas";
    case TransactionPaymentStatus.BELUM_LUNAS:
      return "Belum Lunas";
    default:
      return "Unknown";
  }
};

const getStatusBadgeClasses = (status: TransactionPaymentStatus): string => {
  switch (status) {
    case TransactionPaymentStatus.LUNAS:
      return "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200";
    case TransactionPaymentStatus.BELUM_LUNAS:
      return "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-200";
  }
};

const getStatusSelectItemClasses = (status: TransactionPaymentStatus): string => {
  switch (status) {
    case TransactionPaymentStatus.LUNAS:
      return "focus:bg-green-50 focus:text-green-900 dark:focus:bg-green-900/20 dark:focus:text-green-200";
    case TransactionPaymentStatus.BELUM_LUNAS:
      return "focus:bg-red-50 focus:text-red-900 dark:focus:bg-red-900/20 dark:focus:text-red-200";
    default:
      return "";
  }
};

export const InlineStatusEditor: React.FC<InlineStatusEditorProps> = ({
  saleId,
  currentStatus,
  disabled = false,
}) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [isPending, startTransition] = useTransition();

  const statusOptions = [
    { value: TransactionPaymentStatus.BELUM_LUNAS, label: "Belum Lunas" },
    { value: TransactionPaymentStatus.LUNAS, label: "Lunas" },
  ];

  const handleStatusChange = (newStatus: TransactionPaymentStatus) => {
    if (newStatus === currentStatus) {
      setIsEditing(false);
      return;
    }

    startTransition(async () => {
      try {
        const result = await updateSaleStatus(saleId, newStatus);

        if (result.success) {
          toast.success(result.success);
          router.refresh();
        } else if (result.error) {
          toast.error(result.error);
        }
      } catch (error) {
        console.error("Error updating sale status:", error);
        toast.error("Gagal mengubah status penjualan.");
      } finally {
        setIsEditing(false);
      }
    });
  };

  if (disabled) {
    return (
      <Badge className={getStatusBadgeClasses(currentStatus)}>
        {getStatusDisplayText(currentStatus)}
      </Badge>
    );
  }

  if (isEditing) {
    return (
      <Select
        value={currentStatus}
        onValueChange={(value) => handleStatusChange(value as TransactionPaymentStatus)}
        onOpenChange={(open) => {
          if (!open) {
            setIsEditing(false);
          }
        }}
        defaultOpen={true}
      >
        <SelectTrigger className="w-[140px] h-8">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {statusOptions.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              className={getStatusSelectItemClasses(option.value)}
            >
              <div className="flex items-center gap-2">
                <div
                  className={`w-3 h-3 rounded-full ${getStatusBadgeClasses(option.value)}`}
                />
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {isPending && <LoaderCircle className="h-4 w-4 animate-spin" />}
      <Badge
        className={`cursor-pointer hover:opacity-80 transition-opacity ${getStatusBadgeClasses(currentStatus)}`}
        onClick={() => !isPending && setIsEditing(true)}
      >
        {getStatusDisplayText(currentStatus)}
      </Badge>
    </div>
  );
};
