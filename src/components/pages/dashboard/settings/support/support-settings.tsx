"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  HelpCircle,
  Mail,
  MessageSquare,
  Phone,
  FileText,
  ExternalLink,
  ChevronDown,
  ChevronRight,
  Send,
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  BookOpenText,
  Video,
  Download,
  Search,
  Zap,
  MessageCircle,
  Headphones,
  Globe,
  Calendar,
  Users,
  TrendingUp,
  Award,
  Lightbulb,
  BookOpen,
  PlayCircle,
  FileQuestion,
  LifeBuoy,
  Sparkles,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

export default function SupportSettings() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState("contact");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [category, setCategory] = useState("");
  const [priority, setPriority] = useState("medium");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLiveChatOpen, setIsLiveChatOpen] = useState(false);

  // Handle URL parameters for tabs
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab && ["contact", "faq", "resources", "community"].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleSubmitTicket = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!category || !subject || !message) {
      toast.error("Mohon lengkapi semua field yang wajib diisi!");
      return;
    }

    if (message.length < 20) {
      toast.error("Deskripsi masalah minimal 20 karakter!");
      return;
    }

    setIsSubmitting(true);

    // Simulate API call with more realistic behavior
    setTimeout(() => {
      toast.success(
        "Tiket dukungan berhasil dikirim! Tim kami akan merespons dalam 24 jam."
      );
      setSubject("");
      setMessage("");
      setCategory("");
      setPriority("medium");
      setIsSubmitting(false);
    }, 1500);
  };

  const handleLiveChatClick = () => {
    setIsLiveChatOpen(true);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    const url = new URL(window.location.href);
    url.searchParams.set("tab", tab);
    router.push(url.pathname + url.search, { scroll: false });
  };

  const supportStats = [
    {
      label: "Waktu Respons Rata-rata",
      value: "< 2 jam",
      icon: Clock,
      color: "blue",
    },
    { label: "Tingkat Kepuasan", value: "98%", icon: Star, color: "yellow" },
    {
      label: "Tiket Terselesaikan",
      value: "99.5%",
      icon: CheckCircle,
      color: "green",
    },
    { label: "Tim Support", value: "24/7", icon: Users, color: "purple" },
  ];

  const contactMethods = [
    {
      title: "Email Support",
      description: "Dapatkan bantuan melalui email",
      contact: "<EMAIL>",
      responseTime: "< 24 jam",
      icon: Mail,
      color: "blue",
      available: "24/7",
    },
    {
      title: "Telepon",
      description: "Hubungi langsung tim support",
      contact: "085725300663",
      responseTime: "Langsung",
      icon: Phone,
      color: "green",
      available: "Senin - Jumat, 09:00 - 17:00 WIB",
    },
    {
      title: "Live Chat",
      description: "Chat real-time dengan tim support",
      contact: "Chat tersedia di website",
      responseTime: "< 5 menit",
      icon: MessageCircle,
      color: "purple",
      available: "24/7",
    },
    {
      title: "WhatsApp",
      description: "Support melalui WhatsApp",
      contact: "+62 857-2530-0663",
      responseTime: "< 30 menit",
      icon: MessageSquare,
      color: "emerald",
      available: "Senin - Sabtu, 08:00 - 20:00 WIB",
    },
  ];

  const faqCategories = [
    {
      title: "Akun & Profil",
      icon: Users,
      count: 12,
      questions: [
        {
          question: "Bagaimana cara mengubah password akun?",
          answer:
            "Anda dapat mengubah password melalui halaman Pengaturan > Keamanan. Klik tombol 'Ubah Password' dan ikuti petunjuk yang diberikan.",
        },
        {
          question: "Bagaimana cara mengubah informasi profil?",
          answer:
            "Masuk ke halaman Pengaturan > Profil, kemudian edit informasi yang ingin diubah dan klik 'Simpan Perubahan'.",
        },
        {
          question: "Bagaimana cara menghapus akun?",
          answer:
            "Untuk menghapus akun, silakan hubungi tim support kami melalui email atau live chat. Proses penghapusan akun bersifat permanen dan tidak dapat dibatalkan.",
        },
      ],
    },
    {
      title: "Billing & Pembayaran",
      icon: TrendingUp,
      count: 8,
      questions: [
        {
          question: "Bagaimana cara mengubah paket langganan?",
          answer:
            "Anda dapat mengubah paket langganan melalui halaman Pengaturan > Plan & Tagihan. Pilih paket yang diinginkan dan ikuti petunjuk pembayaran.",
        },
        {
          question: "Metode pembayaran apa saja yang tersedia?",
          answer:
            "Kami menerima pembayaran melalui transfer bank, kartu kredit/debit, e-wallet (GoPay, OVO, DANA), dan virtual account dari berbagai bank.",
        },
        {
          question: "Bagaimana cara mendapatkan invoice?",
          answer:
            "Invoice akan dikirim otomatis ke email Anda setelah pembayaran berhasil. Anda juga dapat mengunduh invoice dari halaman Billing.",
        },
      ],
    },
    {
      title: "Fitur & Penggunaan",
      icon: Zap,
      count: 15,
      questions: [
        {
          question: "Bagaimana cara menambahkan produk baru?",
          answer:
            "Masuk ke halaman Produk, klik tombol 'Tambah Produk', isi informasi produk seperti nama, harga, stok, dan kategori, kemudian klik 'Simpan'.",
        },
        {
          question: "Bagaimana cara melakukan transaksi penjualan?",
          answer:
            "Di halaman Kasir, pilih produk yang akan dijual, masukkan jumlah, pilih metode pembayaran, dan klik 'Proses Pembayaran'.",
        },
        {
          question: "Bagaimana cara melihat laporan penjualan?",
          answer:
            "Masuk ke halaman Laporan, pilih periode yang diinginkan, dan Anda dapat melihat berbagai jenis laporan seperti penjualan harian, bulanan, atau tahunan.",
        },
      ],
    },
    {
      title: "Teknis & Troubleshooting",
      icon: Award,
      count: 10,
      questions: [
        {
          question: "Aplikasi tidak bisa dibuka, apa yang harus dilakukan?",
          answer:
            "Coba refresh halaman atau clear cache browser. Jika masih bermasalah, coba gunakan browser yang berbeda atau hubungi support kami.",
        },
        {
          question: "Data tidak tersinkronisasi, bagaimana solusinya?",
          answer:
            "Pastikan koneksi internet stabil, logout kemudian login kembali. Jika masih bermasalah, hubungi tim support untuk bantuan lebih lanjut.",
        },
        {
          question: "Bagaimana cara backup data?",
          answer:
            "Data Anda otomatis tersimpan di cloud. Untuk backup manual, Anda dapat mengekspor data melalui halaman Pengaturan > Backup & Restore.",
        },
      ],
    },
  ];

  const resources = [
    {
      title: "Panduan Memulai",
      description: "Panduan lengkap untuk memulai menggunakan KivaPOS",
      type: "guide",
      duration: "10 menit",
      icon: BookOpen,
      color: "blue",
      url: "#",
    },
    {
      title: "Video Tutorial Dasar",
      description: "Video tutorial untuk fitur-fitur dasar aplikasi",
      type: "video",
      duration: "15 menit",
      icon: PlayCircle,
      color: "red",
      url: "#",
    },
    {
      title: "API Documentation",
      description: "Dokumentasi lengkap untuk integrasi API",
      type: "docs",
      duration: "30 menit",
      icon: FileText,
      color: "green",
      url: "#",
    },
    {
      title: "Template Invoice",
      description: "Template invoice yang dapat disesuaikan",
      type: "template",
      duration: "5 menit",
      icon: Download,
      color: "purple",
      url: "#",
    },
    {
      title: "Webinar Bulanan",
      description: "Webinar tips dan trik menggunakan aplikasi",
      type: "webinar",
      duration: "60 menit",
      icon: Video,
      color: "orange",
      url: "#",
    },
    {
      title: "Best Practices",
      description: "Panduan best practices untuk bisnis retail",
      type: "guide",
      duration: "20 menit",
      icon: Lightbulb,
      color: "yellow",
      url: "#",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-100 dark:from-gray-900 dark:via-purple-900/20 dark:to-pink-900/20 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="relative overflow-hidden">
          <Card className="border-0 shadow-2xl bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 text-white">
            <CardHeader className="pb-8 relative">
              <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
              <div className="relative z-10">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="h-16 w-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
                      <LifeBuoy className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold mb-2">
                        Pusat Dukungan
                      </h1>
                      <p className="text-purple-100 text-lg">
                        Dapatkan bantuan dan dukungan terbaik untuk aplikasi
                        KivaPOS
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-3 mb-2">
                      <Sparkles className="h-5 w-5 text-purple-200" />
                      <span className="text-purple-100">Status Layanan</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-sm text-purple-100">
                        Semua sistem berjalan normal
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Support Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {supportStats.map((stat, index) => (
            <Card
              key={index}
              className="border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300"
            >
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div
                    className={cn(
                      "h-12 w-12 rounded-xl flex items-center justify-center",
                      stat.color === "blue" &&
                        "bg-gradient-to-br from-blue-500 to-cyan-600",
                      stat.color === "yellow" &&
                        "bg-gradient-to-br from-yellow-500 to-orange-600",
                      stat.color === "green" &&
                        "bg-gradient-to-br from-green-500 to-emerald-600",
                      stat.color === "purple" &&
                        "bg-gradient-to-br from-purple-500 to-pink-600"
                    )}
                  >
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{stat.value}</p>
                    <p className="text-sm text-muted-foreground">
                      {stat.label}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Card className="border-0 shadow-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
          <CardContent className="p-8">
            <Tabs
              value={activeTab}
              onValueChange={handleTabChange}
              className="w-full"
            >
              <TabsList className="grid grid-cols-4 mb-8 bg-gradient-to-r from-gray-100 to-purple-100 dark:from-gray-800 dark:to-purple-900/20 p-1 rounded-2xl h-14">
                <TabsTrigger
                  value="contact"
                  className="flex items-center gap-2 rounded-xl font-semibold data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-purple-600 transition-all duration-200"
                >
                  <MessageSquare className="h-4 w-4" />
                  <span className="hidden sm:inline">Hubungi Kami</span>
                </TabsTrigger>
                <TabsTrigger
                  value="faq"
                  className="flex items-center gap-2 rounded-xl font-semibold data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-purple-600 transition-all duration-200"
                >
                  <FileQuestion className="h-4 w-4" />
                  <span className="hidden sm:inline">FAQ</span>
                </TabsTrigger>
                <TabsTrigger
                  value="resources"
                  className="flex items-center gap-2 rounded-xl font-semibold data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-purple-600 transition-all duration-200"
                >
                  <BookOpenText className="h-4 w-4" />
                  <span className="hidden sm:inline">Sumber Daya</span>
                </TabsTrigger>
                <TabsTrigger
                  value="community"
                  className="flex items-center gap-2 rounded-xl font-semibold data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:text-purple-600 transition-all duration-200"
                >
                  <Users className="h-4 w-4" />
                  <span className="hidden sm:inline">Komunitas</span>
                </TabsTrigger>
              </TabsList>

              {/* Contact Tab */}
              <TabsContent value="contact" className="space-y-8">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Contact Methods */}
                  <div className="lg:col-span-1 space-y-6">
                    <div>
                      <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                        <Headphones className="h-5 w-5 text-purple-600" />
                        Metode Kontak
                      </h3>
                      <p className="text-muted-foreground mb-6">
                        Pilih cara terbaik untuk menghubungi tim support kami
                      </p>
                    </div>

                    <div className="space-y-4">
                      {contactMethods.map((method, index) => (
                        <Card
                          key={index}
                          className="border-2 border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200 cursor-pointer group"
                        >
                          <CardContent className="p-4">
                            <div className="flex items-start gap-4">
                              <div
                                className={cn(
                                  "h-10 w-10 rounded-xl flex items-center justify-center flex-shrink-0",
                                  method.color === "blue" &&
                                    "bg-gradient-to-br from-blue-500 to-cyan-600",
                                  method.color === "green" &&
                                    "bg-gradient-to-br from-green-500 to-emerald-600",
                                  method.color === "purple" &&
                                    "bg-gradient-to-br from-purple-500 to-pink-600",
                                  method.color === "emerald" &&
                                    "bg-gradient-to-br from-emerald-500 to-teal-600"
                                )}
                              >
                                <method.icon className="h-5 w-5 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="font-semibold group-hover:text-purple-600 transition-colors">
                                  {method.title}
                                </h4>
                                <p className="text-sm text-muted-foreground mb-2">
                                  {method.description}
                                </p>
                                <div className="space-y-1">
                                  <p className="text-sm font-medium text-purple-600">
                                    {method.contact}
                                  </p>
                                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {method.responseTime}
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <Globe className="h-3 w-3" />
                                      {method.available}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    {/* Live Chat Dialog */}
                    <Dialog
                      open={isLiveChatOpen}
                      onOpenChange={setIsLiveChatOpen}
                    >
                      <DialogTrigger asChild>
                        <Button
                          className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                          onClick={handleLiveChatClick}
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          Mulai Live Chat
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-md bg-white dark:bg-gray-800 rounded-2xl border-0 shadow-2xl">
                        <DialogHeader>
                          <DialogTitle className="text-xl font-bold flex items-center gap-3">
                            <MessageCircle className="h-6 w-6 text-purple-600" />
                            Live Chat Support
                          </DialogTitle>
                          <DialogDescription>
                            Fitur live chat akan segera tersedia
                          </DialogDescription>
                        </DialogHeader>
                        <div className="py-6">
                          <Alert className="border-l-4 border-purple-500 bg-purple-50 dark:bg-purple-900/20 rounded-xl">
                            <Sparkles className="h-5 w-5 text-purple-600" />
                            <AlertTitle className="text-purple-800 dark:text-purple-200 font-semibold">
                              Segera Hadir
                            </AlertTitle>
                            <AlertDescription className="text-purple-700 dark:text-purple-300">
                              Fitur live chat sedang dalam pengembangan.
                              Sementara ini, Anda dapat menghubungi kami melalui
                              email atau WhatsApp.
                            </AlertDescription>
                          </Alert>
                        </div>
                        <DialogFooter>
                          <Button
                            onClick={() => setIsLiveChatOpen(false)}
                            className="w-full"
                          >
                            Mengerti
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>

                  {/* Support Ticket Form */}
                  <div className="lg:col-span-2">
                    <Card className="border-2 border-dashed border-purple-200 dark:border-purple-800 hover:border-purple-400 dark:hover:border-purple-600 transition-all duration-200 bg-gradient-to-br from-white to-purple-50 dark:from-gray-800 dark:to-purple-900/20">
                      <CardHeader className="pb-6">
                        <CardTitle className="text-2xl font-bold flex items-center gap-3">
                          <Send className="h-6 w-6 text-purple-600" />
                          Kirim Tiket Dukungan
                        </CardTitle>
                        <CardDescription className="text-base">
                          Jelaskan masalah Anda secara detail dan tim dukungan
                          kami akan membantu Anda dalam waktu 24 jam
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <form
                          onSubmit={handleSubmitTicket}
                          className="space-y-6"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label
                                htmlFor="category"
                                className="text-sm font-semibold"
                              >
                                Kategori Masalah{" "}
                                <span className="text-red-500">*</span>
                              </Label>
                              <Select
                                value={category}
                                onValueChange={setCategory}
                                required
                              >
                                <SelectTrigger
                                  id="category"
                                  className="h-12 rounded-xl border-2 border-gray-200 dark:border-gray-700 focus:border-purple-500 dark:focus:border-purple-400 bg-white dark:bg-gray-800"
                                >
                                  <SelectValue placeholder="Pilih kategori masalah" />
                                </SelectTrigger>
                                <SelectContent className="rounded-xl">
                                  <SelectItem value="account">
                                    🔐 Akun & Profil
                                  </SelectItem>
                                  <SelectItem value="billing">
                                    💳 Tagihan & Pembayaran
                                  </SelectItem>
                                  <SelectItem value="technical">
                                    🔧 Masalah Teknis
                                  </SelectItem>
                                  <SelectItem value="feature">
                                    ✨ Permintaan Fitur
                                  </SelectItem>
                                  <SelectItem value="integration">
                                    🔗 Integrasi & API
                                  </SelectItem>
                                  <SelectItem value="other">
                                    📝 Lainnya
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label
                                htmlFor="priority"
                                className="text-sm font-semibold"
                              >
                                Prioritas
                              </Label>
                              <Select
                                value={priority}
                                onValueChange={setPriority}
                              >
                                <SelectTrigger
                                  id="priority"
                                  className="h-12 rounded-xl border-2 border-gray-200 dark:border-gray-700 focus:border-purple-500 dark:focus:border-purple-400 bg-white dark:bg-gray-800"
                                >
                                  <SelectValue placeholder="Pilih prioritas" />
                                </SelectTrigger>
                                <SelectContent className="rounded-xl">
                                  <SelectItem value="low">🟢 Rendah</SelectItem>
                                  <SelectItem value="medium">
                                    🟡 Sedang
                                  </SelectItem>
                                  <SelectItem value="high">
                                    🔴 Tinggi
                                  </SelectItem>
                                  <SelectItem value="urgent">
                                    🚨 Mendesak
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label
                              htmlFor="subject"
                              className="text-sm font-semibold"
                            >
                              Subjek <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="subject"
                              placeholder="Ringkasan singkat masalah Anda"
                              value={subject}
                              onChange={(e) => setSubject(e.target.value)}
                              className="h-12 rounded-xl border-2 border-gray-200 dark:border-gray-700 focus:border-purple-500 dark:focus:border-purple-400 bg-white dark:bg-gray-800 px-4 text-base"
                              required
                            />
                          </div>

                          <div className="space-y-2">
                            <Label
                              htmlFor="message"
                              className="text-sm font-semibold"
                            >
                              Deskripsi Masalah{" "}
                              <span className="text-red-500">*</span>
                            </Label>
                            <Textarea
                              id="message"
                              placeholder="Jelaskan masalah Anda secara detail. Sertakan langkah-langkah yang sudah Anda coba dan informasi lain yang relevan..."
                              rows={6}
                              value={message}
                              onChange={(e) => setMessage(e.target.value)}
                              className="rounded-xl border-2 border-gray-200 dark:border-gray-700 focus:border-purple-500 dark:focus:border-purple-400 bg-white dark:bg-gray-800 px-4 py-3 text-base resize-none"
                              required
                            />
                            <div className="flex justify-between items-center">
                              <p className="text-xs text-muted-foreground">
                                Minimum 20 karakter ({message.length}/20)
                              </p>
                              <Badge
                                variant="secondary"
                                className={cn(
                                  "px-2 py-1 rounded-full text-xs",
                                  message.length >= 20
                                    ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                                    : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300"
                                )}
                              >
                                {message.length >= 20
                                  ? "✓ Valid"
                                  : "Terlalu pendek"}
                              </Badge>
                            </div>
                          </div>

                          <Alert className="border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                            <Lightbulb className="h-5 w-5 text-blue-600" />
                            <AlertTitle className="text-blue-800 dark:text-blue-200 font-semibold">
                              Tips untuk mendapatkan bantuan yang lebih cepat:
                            </AlertTitle>
                            <AlertDescription className="text-blue-700 dark:text-blue-300">
                              <ul className="mt-2 space-y-1 text-sm">
                                <li>
                                  • Sertakan screenshot atau video jika
                                  memungkinkan
                                </li>
                                <li>
                                  • Jelaskan langkah-langkah yang menyebabkan
                                  masalah
                                </li>
                                <li>
                                  • Sebutkan browser dan perangkat yang Anda
                                  gunakan
                                </li>
                                <li>• Cantumkan pesan error jika ada</li>
                              </ul>
                            </AlertDescription>
                          </Alert>

                          <Button
                            type="submit"
                            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-4 text-base font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                            disabled={
                              isSubmitting ||
                              !category ||
                              !subject ||
                              message.length < 20
                            }
                          >
                            {isSubmitting ? (
                              <div className="flex items-center gap-2">
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                Mengirim Tiket...
                              </div>
                            ) : (
                              <div className="flex items-center gap-2">
                                <Send className="h-4 w-4" />
                                Kirim Tiket Dukungan
                              </div>
                            )}
                          </Button>
                        </form>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              {/* FAQ Tab */}
              <TabsContent value="faq" className="space-y-8">
                <div className="flex flex-col lg:flex-row gap-8">
                  <div className="lg:w-1/3">
                    <div className="sticky top-6">
                      <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                        <Search className="h-5 w-5 text-purple-600" />
                        Cari Pertanyaan
                      </h3>
                      <div className="relative mb-6">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Cari FAQ..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10 h-12 rounded-xl border-2 border-gray-200 dark:border-gray-700 focus:border-purple-500 dark:focus:border-purple-400 bg-white dark:bg-gray-800"
                        />
                      </div>

                      <div className="space-y-3">
                        {faqCategories.map((category, index) => (
                          <Card
                            key={index}
                            className="border border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200 cursor-pointer"
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center gap-3">
                                <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                                  <category.icon className="h-4 w-4 text-white" />
                                </div>
                                <div className="flex-1">
                                  <h4 className="font-semibold text-sm">
                                    {category.title}
                                  </h4>
                                  <p className="text-xs text-muted-foreground">
                                    {category.count} pertanyaan
                                  </p>
                                </div>
                                <ChevronRight className="h-4 w-4 text-gray-400" />
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="lg:w-2/3">
                    <h3 className="text-xl font-bold mb-6 flex items-center gap-2">
                      <FileQuestion className="h-5 w-5 text-purple-600" />
                      Pertanyaan Umum
                    </h3>

                    <div className="space-y-6">
                      {faqCategories.map((category, categoryIndex) => (
                        <Card
                          key={categoryIndex}
                          className="border-0 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                        >
                          <CardHeader className="pb-4">
                            <CardTitle className="flex items-center gap-3">
                              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                                <category.icon className="h-4 w-4 text-white" />
                              </div>
                              {category.title}
                              <Badge variant="secondary" className="ml-auto">
                                {category.count}
                              </Badge>
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <Accordion
                              type="single"
                              collapsible
                              className="w-full space-y-2"
                            >
                              {category.questions.map((faq, faqIndex) => (
                                <AccordionItem
                                  key={faqIndex}
                                  value={`${categoryIndex}-${faqIndex}`}
                                  className="border border-gray-200 dark:border-gray-700 rounded-xl px-4 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200"
                                >
                                  <AccordionTrigger className="text-left font-medium hover:no-underline py-4">
                                    {faq.question}
                                  </AccordionTrigger>
                                  <AccordionContent className="text-gray-600 dark:text-gray-300 pb-4 leading-relaxed">
                                    {faq.answer}
                                  </AccordionContent>
                                </AccordionItem>
                              ))}
                            </Accordion>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Resources Tab */}
              <TabsContent value="resources" className="space-y-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-2">
                    Sumber Daya & Panduan
                  </h3>
                  <p className="text-muted-foreground text-lg">
                    Temukan panduan, tutorial, dan sumber daya untuk
                    memaksimalkan penggunaan aplikasi
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {resources.map((resource, index) => (
                    <Card
                      key={index}
                      className="border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300 group cursor-pointer"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4 mb-4">
                          <div
                            className={cn(
                              "h-12 w-12 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-200",
                              resource.color === "blue" &&
                                "bg-gradient-to-br from-blue-500 to-cyan-600",
                              resource.color === "red" &&
                                "bg-gradient-to-br from-red-500 to-pink-600",
                              resource.color === "green" &&
                                "bg-gradient-to-br from-green-500 to-emerald-600",
                              resource.color === "purple" &&
                                "bg-gradient-to-br from-purple-500 to-pink-600",
                              resource.color === "orange" &&
                                "bg-gradient-to-br from-orange-500 to-red-600",
                              resource.color === "yellow" &&
                                "bg-gradient-to-br from-yellow-500 to-orange-600"
                            )}
                          >
                            <resource.icon className="h-6 w-6 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-lg mb-1 group-hover:text-purple-600 transition-colors">
                              {resource.title}
                            </h4>
                            <p className="text-sm text-muted-foreground mb-3">
                              {resource.description}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {resource.duration}
                              </div>
                              <Badge variant="secondary" className="text-xs">
                                {resource.type}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          className="w-full border-2 border-purple-200 text-purple-700 hover:bg-purple-50 dark:border-purple-800 dark:text-purple-300 dark:hover:bg-purple-900/20 rounded-xl group-hover:border-purple-400 transition-all duration-200"
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Akses Resource
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Separator className="my-8 bg-gradient-to-r from-transparent via-purple-300 to-transparent dark:via-purple-600" />

                {/* Featured Content */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <Card className="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-blue-800 dark:text-blue-200">
                        <Video className="h-6 w-6" />
                        Video Tutorial Terbaru
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <PlayCircle className="h-5 w-5 text-blue-600" />
                          <div>
                            <h5 className="font-medium">
                              Cara Setup Awal Aplikasi
                            </h5>
                            <p className="text-sm text-muted-foreground">
                              12 menit • 1.2k views
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <PlayCircle className="h-5 w-5 text-blue-600" />
                          <div>
                            <h5 className="font-medium">
                              Mengelola Inventory dengan Efektif
                            </h5>
                            <p className="text-sm text-muted-foreground">
                              18 menit • 856 views
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <PlayCircle className="h-5 w-5 text-blue-600" />
                          <div>
                            <h5 className="font-medium">
                              Tips Optimasi Laporan Penjualan
                            </h5>
                            <p className="text-sm text-muted-foreground">
                              15 menit • 642 views
                            </p>
                          </div>
                        </div>
                      </div>
                      <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-xl">
                        <PlayCircle className="h-4 w-4 mr-2" />
                        Lihat Semua Video
                      </Button>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-green-800 dark:text-green-200">
                        <Download className="h-6 w-6" />
                        Download Center
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <FileText className="h-5 w-5 text-green-600" />
                          <div>
                            <h5 className="font-medium">
                              Template Invoice Profesional
                            </h5>
                            <p className="text-sm text-muted-foreground">
                              PDF • 2.1 MB
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <FileText className="h-5 w-5 text-green-600" />
                          <div>
                            <h5 className="font-medium">Panduan Lengkap API</h5>
                            <p className="text-sm text-muted-foreground">
                              PDF • 5.8 MB
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <FileText className="h-5 w-5 text-green-600" />
                          <div>
                            <h5 className="font-medium">
                              Template Import Produk
                            </h5>
                            <p className="text-sm text-muted-foreground">
                              Excel • 124 KB
                            </p>
                          </div>
                        </div>
                      </div>
                      <Button className="w-full bg-green-600 hover:bg-green-700 text-white rounded-xl">
                        <Download className="h-4 w-4 mr-2" />
                        Lihat Semua Download
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Community Tab */}
              <TabsContent value="community" className="space-y-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-2">Komunitas KivaPOS</h3>
                  <p className="text-muted-foreground text-lg">
                    Bergabung dengan komunitas pengguna dan dapatkan tips dari
                    sesama pebisnis
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <Card className="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-purple-800 dark:text-purple-200">
                        <Users className="h-6 w-6" />
                        Forum Diskusi
                      </CardTitle>
                      <CardDescription>
                        Diskusi dengan pengguna lain dan berbagi pengalaman
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <h5 className="font-medium mb-1">
                            Tips Meningkatkan Penjualan
                          </h5>
                          <p className="text-sm text-muted-foreground">
                            142 replies • 2.3k views
                          </p>
                        </div>
                        <div className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <h5 className="font-medium mb-1">
                            Integrasi dengan E-commerce
                          </h5>
                          <p className="text-sm text-muted-foreground">
                            89 replies • 1.8k views
                          </p>
                        </div>
                        <div className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <h5 className="font-medium mb-1">
                            Best Practice Inventory Management
                          </h5>
                          <p className="text-sm text-muted-foreground">
                            67 replies • 1.2k views
                          </p>
                        </div>
                      </div>
                      <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white rounded-xl">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Bergabung Forum
                      </Button>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-xl bg-gradient-to-br from-orange-50 to-red-100 dark:from-orange-900/20 dark:to-red-900/20">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-orange-800 dark:text-orange-200">
                        <Calendar className="h-6 w-6" />
                        Event & Webinar
                      </CardTitle>
                      <CardDescription>
                        Ikuti webinar dan event eksklusif untuk pengguna
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <h5 className="font-medium mb-1">
                            Webinar: Digital Marketing untuk UMKM
                          </h5>
                          <p className="text-sm text-muted-foreground">
                            25 Jan 2024 • 19:00 WIB
                          </p>
                        </div>
                        <div className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <h5 className="font-medium mb-1">
                            Workshop: Analisis Laporan Keuangan
                          </h5>
                          <p className="text-sm text-muted-foreground">
                            2 Feb 2024 • 14:00 WIB
                          </p>
                        </div>
                        <div className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                          <h5 className="font-medium mb-1">
                            Meetup: Komunitas Pengguna Jakarta
                          </h5>
                          <p className="text-sm text-muted-foreground">
                            10 Feb 2024 • 10:00 WIB
                          </p>
                        </div>
                      </div>
                      <Button className="w-full bg-orange-600 hover:bg-orange-700 text-white rounded-xl">
                        <Calendar className="h-4 w-4 mr-2" />
                        Lihat Semua Event
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                <Separator className="my-8 bg-gradient-to-r from-transparent via-purple-300 to-transparent dark:via-purple-600" />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="border-0 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-center">
                    <CardContent className="p-6">
                      <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center mx-auto mb-4">
                        <Users className="h-6 w-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-lg mb-2">12,000+</h4>
                      <p className="text-sm text-muted-foreground">
                        Anggota Aktif
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-center">
                    <CardContent className="p-6">
                      <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center mx-auto mb-4">
                        <MessageSquare className="h-6 w-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-lg mb-2">850+</h4>
                      <p className="text-sm text-muted-foreground">
                        Diskusi Bulanan
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-center">
                    <CardContent className="p-6">
                      <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center mx-auto mb-4">
                        <Award className="h-6 w-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-lg mb-2">95%</h4>
                      <p className="text-sm text-muted-foreground">
                        Tingkat Kepuasan
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
