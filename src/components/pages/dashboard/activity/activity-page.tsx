"use client";

import React, { useEffect } from "react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  ClockIcon,
  FunnelIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CubeIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UserIcon,
  CalendarDaysIcon,
  UsersIcon,
} from "@heroicons/react/24/outline";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ActivityItem, ActivityType } from "@/actions/activity/activity";
import { useActivityStore } from "@/stores/activity-store";
import { cn } from "@/lib/utils";
import { UserActivity } from "@/actions/users/activity";

const ITEMS_PER_PAGE = 10;

export default function ActivityPage() {
  const {
    activities,
    loading,
    error,
    totalCount,
    currentPage,
    activeTab,
    dateRange,
    employeeOnly,
    fetchActivities,
    setPage,
    setTab,
    setDateRange,
    setEmployeeOnly,
    clearFilters,
    refresh,
  } = useActivityStore();

  useEffect(() => {
    fetchActivities();
  }, [fetchActivities]);

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= totalPages) {
      setPage(page);
    }
  };

  const handleTabChange = (value: string) => {
    setTab(value as ActivityType | "all");
  };

  const handleDateRangeChange = (
    field: "startDate" | "endDate",
    date: Date | undefined
  ) => {
    if (date) {
      setDateRange(field, format(date, "yyyy-MM-dd"));
    } else {
      setDateRange(field, "");
    }
  };

  const handleEmployeeFilterChange = (value: string) => {
    setEmployeeOnly(value === "true");
  };

  const handleRefresh = () => {
    refresh();
  };

  const handleClearFilters = () => {
    clearFilters();
  };

  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  const getActivityIcon = (type: string) => {
    const iconClass = "h-5 w-5";
    switch (type) {
      case "sale":
        return (
          <CurrencyDollarIcon className={`${iconClass} text-emerald-600`} />
        );
      case "purchase":
        return <ShoppingBagIcon className={`${iconClass} text-blue-600`} />;
      case "product":
        return <CubeIcon className={`${iconClass} text-purple-600`} />;
      case "login":
        return <UserIcon className={`${iconClass} text-amber-600`} />;
      default:
        return <ClockIcon className={`${iconClass} text-slate-600`} />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case "sale":
        return "bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950/50 dark:text-emerald-300 dark:border-emerald-800/50";
      case "purchase":
        return "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950/50 dark:text-blue-300 dark:border-blue-800/50";
      case "product":
        return "bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950/50 dark:text-purple-300 dark:border-purple-800/50";
      case "login":
        return "bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/50 dark:text-amber-300 dark:border-amber-800/50";
      default:
        return "bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-800/50 dark:text-slate-300 dark:border-slate-700/50";
    }
  };

  const getActivityBadgeIcon = (type: string) => {
    const iconClass = "h-3 w-3";
    switch (type) {
      case "sale":
        return <CurrencyDollarIcon className={iconClass} />;
      case "purchase":
        return <ShoppingBagIcon className={iconClass} />;
      case "product":
        return <CubeIcon className={iconClass} />;
      case "login":
        return <UserIcon className={iconClass} />;
      default:
        return <ClockIcon className={iconClass} />;
    }
  };

  const isActivityItem = (
    activity: ActivityItem | UserActivity
  ): activity is ActivityItem => {
    return "performedBy" in activity && "isEmployee" in activity;
  };

  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-b border-slate-200/60 dark:border-slate-700/60 sticky top-0 z-10">
        <div className="mx-auto px-2 lg:px-4">
          <div className="py-6 sm:py-8">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                  <ClockIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                    Aktivitas Sistem
                  </h1>
                  <p className="text-slate-600 dark:text-slate-400 text-sm sm:text-base">
                    Pantau semua aktivitas dan perubahan dalam sistem
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2 sm:ml-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  className="flex items-center gap-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200 dark:border-slate-700 hover:bg-white dark:hover:bg-slate-800 cursor-pointer"
                >
                  <ArrowPathIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Refresh</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearFilters}
                  className="flex items-center gap-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200 dark:border-slate-700 hover:bg-white dark:hover:bg-slate-800 cursor-pointer"
                >
                  <FunnelIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Reset</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="mx-auto py-6 sm:py-8">
        <Card className="shadow-xl bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-0 ring-1 ring-slate-200/60 dark:ring-slate-700/60">
          <CardHeader className="pb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle className="text-xl sm:text-2xl font-semibold text-slate-900 dark:text-white">
                  Riwayat Aktivitas
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-400 mt-1">
                  Semua aktivitas yang terjadi di sistem dalam waktu real-time
                </CardDescription>
              </div>

              {/* Activity Stats */}
              <div className="flex items-center gap-6 text-sm text-slate-600 dark:text-slate-400">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse"></div>
                  <span>Live</span>
                </div>
                <div className="hidden sm:flex items-center gap-2">
                  <span className="font-medium text-slate-900 dark:text-white">
                    {totalCount}
                  </span>
                  <span>Total</span>
                </div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Enhanced Filter Section */}
            <div className="space-y-6">
              {/* Activity Type Tabs */}
              <Tabs
                defaultValue="all"
                value={activeTab}
                onValueChange={handleTabChange}
                className="w-full"
              >
                <TabsList className="grid grid-cols-5 h-12 bg-slate-100/80 dark:bg-slate-700/50 p-1 rounded-xl w-full md:w-fit">
                  <TabsTrigger
                    value="all"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <span className="hidden sm:inline">Semua</span>
                    <span className="sm:hidden">All</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="sale"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <CurrencyDollarIcon className="h-4 w-4 sm:hidden" />
                    <span className="hidden sm:inline">Penjualan</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="purchase"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <ShoppingBagIcon className="h-4 w-4 sm:hidden" />
                    <span className="hidden sm:inline">Pembelian</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="product"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <CubeIcon className="h-4 w-4 sm:hidden" />
                    <span className="hidden sm:inline">Produk</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="login"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <UserIcon className="h-4 w-4 sm:hidden" />
                    <span className="hidden sm:inline">Login</span>
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Date and Employee Filters */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="startDate"
                    className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2"
                  >
                    <CalendarDaysIcon className="h-4 w-4" />
                    Tanggal Mulai
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 !h-11",
                          !dateRange.startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarDaysIcon className="mr-2 h-4 w-4" />
                        {dateRange.startDate ? (
                          format(new Date(dateRange.startDate), "PPP", {
                            locale: id,
                          })
                        ) : (
                          <span>Pilih tanggal</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={
                          dateRange.startDate
                            ? new Date(dateRange.startDate)
                            : undefined
                        }
                        onSelect={(date) =>
                          handleDateRangeChange("startDate", date)
                        }
                        initialFocus
                        locale={id}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="endDate"
                    className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2"
                  >
                    <CalendarDaysIcon className="h-4 w-4" />
                    Tanggal Akhir
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 !h-11",
                          !dateRange.endDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarDaysIcon className="mr-2 h-4 w-4" />
                        {dateRange.endDate ? (
                          format(new Date(dateRange.endDate), "PPP", {
                            locale: id,
                          })
                        ) : (
                          <span>Pilih tanggal</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={
                          dateRange.endDate
                            ? new Date(dateRange.endDate)
                            : undefined
                        }
                        onSelect={(date) =>
                          handleDateRangeChange("endDate", date)
                        }
                        initialFocus
                        locale={id}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="employeeFilter"
                    className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2"
                  >
                    <UsersIcon className="h-4 w-4" />
                    Filter Karyawan
                  </Label>
                  <Select
                    value={employeeOnly ? "true" : "false"}
                    onValueChange={handleEmployeeFilterChange}
                  >
                    <SelectTrigger
                      id="employeeFilter"
                      className="bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 focus:ring-2 focus:ring-indigo-500"
                    >
                      <SelectValue placeholder="Pilih filter karyawan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="false">Semua Aktivitas</SelectItem>
                      <SelectItem value="true">
                        Hanya Aktivitas Karyawan
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="h-px bg-gradient-to-r from-transparent via-slate-200 dark:via-slate-700 to-transparent" />

            {/* Activity List */}
            <div className="space-y-3">
              {loading ? (
                // Enhanced Loading skeleton
                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-4 p-4 sm:p-6 bg-white/60 dark:bg-slate-700/30 rounded-2xl border border-slate-200/60 dark:border-slate-600/60"
                    >
                      <Skeleton className="h-12 w-12 rounded-2xl" />
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-6 w-20 rounded-full" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                        <Skeleton className="h-5 w-full max-w-md" />
                        <Skeleton className="h-4 w-32" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : error ? (
                <div className="p-6 text-red-600 bg-red-50 dark:bg-red-900/20 rounded-2xl border border-red-200 dark:border-red-800/50">
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-full bg-red-100 dark:bg-red-900/40 flex items-center justify-center">
                      <ClockIcon className="h-4 w-4 text-red-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Terjadi Kesalahan</h3>
                      <p className="text-sm text-red-500 dark:text-red-400">
                        {error}
                      </p>
                    </div>
                  </div>
                </div>
              ) : activities.length === 0 ? (
                <div className="p-12 text-center">
                  <div className="mx-auto h-20 w-20 rounded-3xl bg-slate-100 dark:bg-slate-700 flex items-center justify-center mb-6">
                    <ClockIcon className="h-10 w-10 text-slate-400 dark:text-slate-500" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    Tidak ada aktivitas
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto">
                    Tidak ada aktivitas yang ditemukan dengan filter yang
                    dipilih. Coba ubah kriteria pencarian Anda.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {activities.map(
                    (activity: ActivityItem | UserActivity, index: number) => (
                      <div
                        key={activity.id}
                        className="group flex items-start gap-4 p-4 sm:p-6 bg-white/80 dark:bg-slate-700/30 rounded-2xl border border-slate-200/60 dark:border-slate-600/60 hover:bg-white dark:hover:bg-slate-700/50 hover:shadow-lg hover:scale-[1.01] transition-all duration-200 cursor-pointer"
                        style={{
                          animationDelay: `${index * 100}ms`,
                          animation: "fadeInUp 0.5s ease-out forwards",
                        }}
                      >
                        <div className="h-12 w-12 rounded-2xl bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-600 dark:to-slate-700 flex items-center justify-center group-hover:scale-110 transition-transform duration-200 shadow-sm">
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                            <Badge
                              variant="outline"
                              className={`w-fit text-xs font-medium px-3 py-1 rounded-full border ${getActivityColor(activity.type)} flex items-center gap-1.5`}
                            >
                              {getActivityBadgeIcon(activity.type)}
                              {activity.type.toUpperCase()}
                            </Badge>
                            <span className="text-xs text-slate-500 dark:text-slate-400 flex items-center gap-1">
                              <ClockIcon className="h-3 w-3" />
                              {activity.timestamp}
                            </span>
                          </div>
                          <p className="text-sm sm:text-base font-medium text-slate-900 dark:text-white mb-2 leading-relaxed">
                            {activity.description}
                          </p>
                          <div className="flex flex-wrap items-center gap-3 text-xs text-slate-500 dark:text-slate-400">
                            {isActivityItem(activity) &&
                              activity.performedBy && (
                                <div className="flex items-center gap-1.5">
                                  <UserIcon className="h-3 w-3" />
                                  <span>oleh</span>
                                  <span className="font-medium text-slate-700 dark:text-slate-300">
                                    {activity.performedBy}
                                  </span>
                                </div>
                              )}
                            {isActivityItem(activity) &&
                              activity.isEmployee && (
                                <Badge
                                  variant="outline"
                                  className="text-xs bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950/50 dark:text-indigo-300 dark:border-indigo-800/50 px-2 py-0.5 rounded-full"
                                >
                                  Karyawan
                                </Badge>
                              )}
                          </div>
                        </div>
                      </div>
                    )
                  )}
                </div>
              )}
            </div>

            {/* Enhanced Pagination */}
            {!loading && !error && totalPages > 0 && (
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pt-6 border-t border-slate-200/60 dark:border-slate-700/60">
                <div className="text-sm text-slate-600 dark:text-slate-400 text-center sm:text-left">
                  Menampilkan{" "}
                  <span className="font-medium text-slate-900 dark:text-white">
                    {activities.length}
                  </span>{" "}
                  dari{" "}
                  <span className="font-medium text-slate-900 dark:text-white">
                    {totalCount}
                  </span>{" "}
                  aktivitas
                </div>
                <div className="flex items-center justify-center sm:justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="h-10 w-10 p-0 rounded-xl bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 hover:bg-white dark:hover:bg-slate-700 disabled:opacity-40"
                  >
                    <ChevronLeftIcon className="h-4 w-4" />
                  </Button>

                  <div className="flex items-center gap-2 px-4 py-2 bg-slate-100/80 dark:bg-slate-700/50 rounded-xl">
                    <span className="text-sm font-medium text-slate-900 dark:text-white">
                      {currentPage}
                    </span>
                    <span className="text-sm text-slate-400">dari</span>
                    <span className="text-sm font-medium text-slate-900 dark:text-white">
                      {totalPages}
                    </span>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="h-10 w-10 p-0 rounded-xl bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 hover:bg-white dark:hover:bg-slate-700 disabled:opacity-40"
                  >
                    <ChevronRightIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
}
