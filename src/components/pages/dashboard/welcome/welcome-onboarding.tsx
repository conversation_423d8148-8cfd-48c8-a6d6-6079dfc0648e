"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Spinner } from "@/components/ui/spinner";
import { logout } from "@/actions/auth/logout";

// Import components
import StepIndicator from "./components/StepIndicator";
import BusinessProfileStep from "./components/BusinessProfileStep";
import AccountPreferencesStep from "./components/AccountPreferencesStep";

// Import types and schemas
import { OnboardingFormData, OnboardingStep, onboardingSchema } from "./types";

// Import server actions
import { saveOnboardingData } from "@/actions/onboarding/onboarding";

const WelcomeOnboarding: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // Form setup
  const form = useForm<OnboardingFormData>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      companyName: "",
      companyUsername: "",
      phoneNumber: "",
      password: "",
      position: "",
      employeeCount: "",
      occupation: "",
      industry: "",
      subscriptionPackage: "",
      referralCode: "",
    },
    mode: "onChange",
  });

  const { handleSubmit, trigger } = form;

  // Navigation functions
  const goToNextStep = async () => {
    let isValid = false;

    if (currentStep === 1) {
      // Validate step 1 fields
      isValid = await trigger([
        "companyName",
        "companyUsername",
        "phoneNumber",
        "password",
        "position",
        "employeeCount",
      ]);
    }

    if (isValid) {
      setCurrentStep(2);
    } else {
      toast.error("Mohon lengkapi semua field yang wajib diisi");
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => (prev - 1) as OnboardingStep);
    }
  };

  // Form submission
  const onSubmit = async (data: OnboardingFormData) => {
    setIsSubmitting(true);

    try {
      // Save onboarding data to database
      const result = await saveOnboardingData(data);

      if (result.success) {
        toast.success("Selamat! Profil bisnis Anda berhasil dibuat", {
          description: "Anda akan diarahkan ke dashboard dalam beberapa detik",
        });

        // Redirect to dashboard after successful submission
        setTimeout(() => {
          router.push("/dashboard/summaries");
        }, 2000);
      } else {
        toast.error("Gagal menyimpan data", {
          description: result.error || "Silakan coba lagi atau hubungi support",
        });
      }
    } catch (error) {
      console.error("Onboarding error:", error);
      toast.error("Terjadi kesalahan saat menyimpan data", {
        description: "Silakan coba lagi atau hubungi support",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8 px-4 overflow-y-auto">
      <div className="flex flex-col items-center min-h-screen">
        {/* Top Navigation Breadcrumb */}
        <div className="w-full flex justify-center mb-8">
          <div className="flex items-center gap-3 text-sm">
            <span className="text-gray-600 font-medium">Profil bisnis</span>
            <span className="text-gray-400">•</span>
            <span className="text-blue-600 font-semibold">
              {currentStep === 1 ? "Lengkapi Profil Bisnis" : "Preferensi Akun"}
            </span>
            <span className="text-gray-400">•</span>
            <button
              onClick={async () => {
                await logout();
              }}
              className="text-red-600 cursor-pointer font-semibold hover:underline"
            >
              Logout
            </button>
          </div>
        </div>

        {/* Main Card */}
        <Card className="w-full max-w-2xl bg-white shadow-lg border-0 rounded-lg mb-8">
          <CardContent className="lg:px-14 px-6 py-4">
            {/* Logo and Title */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-6">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl shadow-lg flex items-center justify-center">
                    <span className="text-2xl font-bold text-white">K</span>
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                </div>
                <div className="ml-4 text-left">
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                    KivaPOS
                  </h1>
                  <p className="text-sm text-gray-500 font-medium">
                    Solusi Kasir Modern
                  </p>
                </div>
              </div>
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {currentStep === 1 ? "" : ""}
                </h2>
                <p className="text-gray-600 text-sm">
                  {currentStep === 1
                    ? "Masukkan informasi dasar perusahaan Anda"
                    : "Sesuaikan pengaturan sesuai kebutuhan bisnis"}
                </p>
              </div>
              <StepIndicator currentStep={currentStep} totalSteps={2} />
            </div>

            {/* Form Content */}
            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Step Content */}
                <div>
                  {currentStep === 1 && (
                    <BusinessProfileStep
                      control={form.control}
                      isPending={isSubmitting}
                    />
                  )}

                  {currentStep === 2 && (
                    <AccountPreferencesStep
                      control={form.control}
                      isPending={isSubmitting}
                    />
                  )}
                </div>

                {/* Navigation Buttons */}
                <div className="space-y-3">
                  {currentStep === 1 ? (
                    <Button
                      type="button"
                      onClick={goToNextStep}
                      disabled={isSubmitting}
                      className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium cursor-pointer transition-all duration-200 hover:shadow-lg"
                    >
                      Lanjut
                    </Button>
                  ) : (
                    <>
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium cursor-pointer transition-all duration-200 hover:shadow-lg"
                      >
                        {isSubmitting ? (
                          <>
                            <Spinner size="small" className="mr-2" />
                            Menyimpan...
                          </>
                        ) : (
                          "Mulai Gunakan KivaPOS"
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={goToPreviousStep}
                        disabled={isSubmitting}
                        className="w-full h-12 border-gray-300 text-gray-700 hover:bg-gray-50 cursor-pointer transition-all duration-200"
                      >
                        Kembali
                      </Button>
                    </>
                  )}
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="w-full flex flex-col items-center text-center mt-auto py-8 px-4">
          <div className="flex items-center justify-center gap-6 text-xs text-gray-500 my-2">
            <button className="hover:text-blue-600 transition-colors cursor-pointer">
              Kebijakan privasi
            </button>
            <span className="text-gray-300">•</span>
            <button className="hover:text-blue-600 transition-colors cursor-pointer">
              Ketentuan penggunaan
            </button>
            <span className="text-gray-300">•</span>
            <button className="hover:text-blue-600 transition-colors cursor-pointer">
              Tentang KivaPOS
            </button>
          </div>
          <div className="flex items-center justify-center gap-3 my-4">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center shadow-md">
              <span className="text-sm font-bold text-white">K</span>
            </div>
            <span className="text-lg font-bold text-gray-900">KivaPOS</span>
          </div>
          <p className="text-xs text-gray-500">
            © 2024 KivaPOS. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeOnboarding;
