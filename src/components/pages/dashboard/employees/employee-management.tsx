"use client";

import { deleteEmployee, getEmployees } from "@/actions/entities/employee";
import { Role } from "@prisma/client";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  UserPlus,
  Users,
  AlertCircle,
  Shield,
  Crown,
  User,
  TrendingUp,
  Activity,
  Clock,
  Search,
  Filter,
  MoreVertical,
  Settings,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { EmployeeTable } from "../settings/employees/components/employee-table";
import { AddEmployeeDialog } from "../settings/employees/components/add-employee-dialog";
import { EditEmployeeNameDialog } from "../settings/employees/components/edit-employee-name-dialog";
import { EditEmployeePasswordDialog } from "../settings/employees/components/edit-employee-password-dialog";

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role;
  createdAt?: Date;
}

export default function EmployeeManagement() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditNameDialogOpen, setIsEditNameDialogOpen] = useState(false);
  const [isEditPasswordDialogOpen, setIsEditPasswordDialogOpen] =
    useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState("");

  const getCurrentTab = () => {
    if (searchParams.has("admin")) return "admin";
    if (searchParams.has("kasir")) return "cashier";
    if (searchParams.has("semua")) return "all";
    return "all";
  };

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const currentPath = window.location.pathname;
    let newUrl = currentPath;

    if (value === "admin") {
      newUrl = `${currentPath}?admin`;
    } else if (value === "cashier") {
      newUrl = `${currentPath}?kasir`;
    } else if (value === "all") {
      newUrl = `${currentPath}?semua`;
    } else {
      newUrl = currentPath;
    }

    router.replace(newUrl, { scroll: false });
  };

  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [searchParams]);

  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const result = await getEmployees();
      if (result.error) {
        setError(result.error);
      } else if (result.employees) {
        setEmployees(result.employees);
      }
    } catch (err) {
      setError("Terjadi kesalahan saat mengambil data karyawan");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEmployee = async (id: string) => {
    if (confirm("Apakah Anda yakin ingin menghapus karyawan ini?")) {
      try {
        const result = await deleteEmployee(id);

        if (result.error) {
          toast.error(result.error);
        } else {
          toast.success(result.success);
          fetchEmployees();
        }
      } catch (err) {
        toast.error("Terjadi kesalahan saat menghapus karyawan");
        console.error(err);
      }
    }
  };

  const handleEditNameClick = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEditNameDialogOpen(true);
  };

  const handleEditPasswordClick = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEditPasswordDialogOpen(true);
  };

  // Filter employees based on search term
  const filteredEmployees = employees.filter(
    (employee) =>
      employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Loading state with modern skeleton
  if (loading && employees.length === 0) {
    return (
      <div className="space-y-6">
        {/* Hero Skeleton */}
        <div className="h-32 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-2xl animate-pulse"></div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className="h-24 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse"
            ></div>
          ))}
        </div>

        {/* Main Content Skeleton */}
        <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse"></div>
      </div>
    );
  }

  if (error && employees.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4 text-red-500">
        <AlertCircle className="h-8 w-8" />
        <p>{error}</p>
      </div>
    );
  }

  const adminCount = employees.filter((e) => e.role === "ADMIN").length;
  const cashierCount = employees.filter((e) => e.role === "CASHIER").length;

  return (
    <div className="space-y-6 min-h-[100vh]">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                  Total Karyawan
                </p>
                <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">
                  {employees.length}
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                  Administrator
                </p>
                <p className="text-3xl font-bold text-purple-700 dark:text-purple-300">
                  {adminCount}
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <Crown className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">
                  Kasir
                </p>
                <p className="text-3xl font-bold text-green-700 dark:text-green-300">
                  {cashierCount}
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <User className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">
                  Aktif Hari Ini
                </p>
                <p className="text-3xl font-bold text-orange-700 dark:text-orange-300">
                  {employees.length}
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="border-b border-gray-100 dark:border-gray-800">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle className="text-xl">Daftar Karyawan</CardTitle>
              <CardDescription>
                Kelola karyawan dan akses mereka ke sistem
              </CardDescription>
            </div>
            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Cari karyawan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Button
                className="bg-gradient-to-r from-orange-500 to-amber-600 hover:from-orange-600 hover:to-amber-700 text-white cursor-pointer"
                onClick={() => setIsAddDialogOpen(true)}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Tambah Karyawan
              </Button>
            </div>
          </div>
        </CardHeader>

        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <div className="px-6 pt-4 border-b border-gray-100 dark:border-gray-800">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger
                value="all"
                className="flex items-center gap-2 cursor-pointer"
              >
                <Users className="h-4 w-4" />
                Semua
                <Badge
                  variant="secondary"
                  className="ml-2 bg-gray-100 dark:bg-gray-800"
                >
                  {filteredEmployees.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="admin"
                className="flex items-center gap-2 cursor-pointer"
              >
                <Crown className="h-4 w-4" />
                Admin
                <Badge
                  variant="secondary"
                  className="ml-2 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-300"
                >
                  {filteredEmployees.filter((e) => e.role === "ADMIN").length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="cashier"
                className="flex items-center gap-2 cursor-pointer"
              >
                <User className="h-4 w-4" />
                Kasir
                <Badge
                  variant="secondary"
                  className="ml-2 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-300"
                >
                  {filteredEmployees.filter((e) => e.role === "CASHIER").length}
                </Badge>
              </TabsTrigger>
            </TabsList>
          </div>

          <CardContent className="pt-6">
            <TabsContent value="all" className="mt-0">
              <EmployeeTable
                employees={filteredEmployees}
                onEditName={handleEditNameClick}
                onEditPassword={handleEditPasswordClick}
                onDelete={handleDeleteEmployee}
              />
            </TabsContent>

            <TabsContent value="admin" className="mt-0">
              <EmployeeTable
                employees={filteredEmployees.filter((e) => e.role === "ADMIN")}
                onEditName={handleEditNameClick}
                onEditPassword={handleEditPasswordClick}
                onDelete={handleDeleteEmployee}
              />
            </TabsContent>

            <TabsContent value="cashier" className="mt-0">
              <EmployeeTable
                employees={filteredEmployees.filter(
                  (e) => e.role === "CASHIER"
                )}
                onEditName={handleEditNameClick}
                onEditPassword={handleEditPasswordClick}
                onDelete={handleDeleteEmployee}
              />
            </TabsContent>
          </CardContent>
        </Tabs>
      </Card>

      {/* Dialogs */}
      <AddEmployeeDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSuccess={fetchEmployees}
      />

      <EditEmployeeNameDialog
        employee={selectedEmployee}
        open={isEditNameDialogOpen}
        onOpenChange={setIsEditNameDialogOpen}
        onSuccess={fetchEmployees}
      />

      <EditEmployeePasswordDialog
        employee={selectedEmployee}
        open={isEditPasswordDialogOpen}
        onOpenChange={setIsEditPasswordDialogOpen}
      />
    </div>
  );
}
