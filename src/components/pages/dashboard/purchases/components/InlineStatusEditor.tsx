"use client";

import React, { useState, useTransition } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { LoaderCircle } from "lucide-react";
import { TransactionPaymentStatus } from "../types";
import { updatePurchaseStatus } from "@/actions/entities/purchases";

interface InlineStatusEditorProps {
  purchaseId: string;
  currentStatus: TransactionPaymentStatus;
  disabled?: boolean;
}

const getStatusDisplayText = (status: TransactionPaymentStatus): string => {
  console.log(
    "[InlineStatusEditor] Purchase status value:",
    status,
    "Type:",
    typeof status
  );

  // Handle null/undefined status by defaulting to BELUM_LUNAS
  const normalizedStatus = status || TransactionPaymentStatus.BELUM_LUNAS;

  switch (normalizedStatus) {
    case TransactionPaymentStatus.LUNAS:
      return "Lunas";
    case TransactionPaymentStatus.BELUM_LUNAS:
      return "Belum Lunas";
    default:
      console.log("[InlineStatusEditor] Unknown status value:", status);
      return "Belum Lunas"; // Default to "Belum Lunas" instead of "Unknown"
  }
};

const getStatusBadgeClasses = (status: TransactionPaymentStatus): string => {
  // Handle null/undefined status by defaulting to BELUM_LUNAS
  const normalizedStatus = status || TransactionPaymentStatus.BELUM_LUNAS;

  switch (normalizedStatus) {
    case TransactionPaymentStatus.LUNAS:
      return "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200";
    case TransactionPaymentStatus.BELUM_LUNAS:
      return "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200";
    default:
      return "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200"; // Default to "Belum Lunas" styling
  }
};

const getStatusSelectItemClasses = (
  status: TransactionPaymentStatus
): string => {
  // Handle null/undefined status by defaulting to BELUM_LUNAS
  const normalizedStatus = status || TransactionPaymentStatus.BELUM_LUNAS;

  switch (normalizedStatus) {
    case TransactionPaymentStatus.LUNAS:
      return "focus:bg-green-50 focus:text-green-900 dark:focus:bg-green-900/20 dark:focus:text-green-200";
    case TransactionPaymentStatus.BELUM_LUNAS:
      return "focus:bg-red-50 focus:text-red-900 dark:focus:bg-red-900/20 dark:focus:text-red-200";
    default:
      return "focus:bg-red-50 focus:text-red-900 dark:focus:bg-red-900/20 dark:focus:text-red-200"; // Default to "Belum Lunas" styling
  }
};

export const InlineStatusEditor: React.FC<InlineStatusEditorProps> = ({
  purchaseId,
  currentStatus,
  disabled = false,
}) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [isPending, startTransition] = useTransition();

  // Normalize the current status to handle null/undefined values
  const normalizedCurrentStatus =
    currentStatus || TransactionPaymentStatus.BELUM_LUNAS;

  const statusOptions = [
    { value: TransactionPaymentStatus.BELUM_LUNAS, label: "Belum Lunas" },
    { value: TransactionPaymentStatus.LUNAS, label: "Lunas" },
  ];

  const handleStatusChange = (newStatus: TransactionPaymentStatus) => {
    if (newStatus === currentStatus) {
      setIsEditing(false);
      return;
    }

    startTransition(async () => {
      try {
        const result = await updatePurchaseStatus(purchaseId, newStatus);

        if (result.success) {
          toast.success(result.success);
          router.refresh();
        } else if (result.error) {
          toast.error(result.error);
        }
      } catch (error) {
        console.error("Error updating purchase status:", error);
        toast.error("Gagal mengubah status pembelian.");
      } finally {
        setIsEditing(false);
      }
    });
  };

  if (disabled) {
    return (
      <Badge className={getStatusBadgeClasses(normalizedCurrentStatus)}>
        {getStatusDisplayText(normalizedCurrentStatus)}
      </Badge>
    );
  }

  if (isEditing) {
    return (
      <Select
        value={currentStatus || TransactionPaymentStatus.BELUM_LUNAS}
        onValueChange={(value) =>
          handleStatusChange(value as TransactionPaymentStatus)
        }
        onOpenChange={(open) => {
          if (!open) {
            setIsEditing(false);
          }
        }}
        defaultOpen={true}
      >
        <SelectTrigger className="w-[140px] h-8">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {statusOptions.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              className={getStatusSelectItemClasses(option.value)}
            >
              <div className="flex items-center gap-2">
                <div
                  className={`w-3 h-3 rounded-full ${getStatusBadgeClasses(option.value)}`}
                />
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {isPending && <LoaderCircle className="h-4 w-4 animate-spin" />}
      <Badge
        className={`cursor-pointer hover:opacity-80 transition-opacity ${getStatusBadgeClasses(normalizedCurrentStatus)}`}
        onClick={() => !isPending && setIsEditing(true)}
      >
        {getStatusDisplayText(normalizedCurrentStatus)}
      </Badge>
    </div>
  );
};
