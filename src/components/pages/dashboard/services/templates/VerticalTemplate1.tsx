import type { Service } from "../types";

/**
 * Vertical Template 1 - Professional service invoice template with vertical orientation
 */
export const renderVerticalTemplate1 = (service: Service): string => {
  const finalCost = service.finalCost || service.estimatedCost || 0;
  const costNumber =
    typeof finalCost === "number" ? finalCost : Number(finalCost);

  return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Faktur Servis - ${service.serviceNumber}</title>
      <style>
        @page {
          size: A4 portrait;
          margin: 2cm;
        }
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
          color: #000;
          background-color: white;
          font-size: 11pt;
          line-height: 1.4;
        }
        .invoice-container {
          max-width: 21cm;
          margin: 0 auto;
          padding: 20px;
          box-sizing: border-box;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 3px solid #4F46E5;
          padding-bottom: 20px;
        }
        .company-name {
          font-size: 24pt;
          font-weight: bold;
          color: #4F46E5;
          margin-bottom: 5px;
        }
        .company-tagline {
          font-size: 12pt;
          color: #666;
          margin-bottom: 15px;
        }
        .company-details {
          font-size: 10pt;
          color: #666;
          line-height: 1.6;
        }
        .invoice-title {
          font-size: 22pt;
          font-weight: bold;
          color: #4F46E5;
          margin: 20px 0 10px 0;
        }
        .invoice-meta {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
          background-color: #F9FAFB;
          padding: 15px;
          border-radius: 8px;
        }
        .invoice-number {
          font-size: 14pt;
          font-weight: bold;
        }
        .invoice-date {
          font-size: 12pt;
          color: #666;
        }
        .section {
          margin-bottom: 25px;
        }
        .section-title {
          font-size: 14pt;
          font-weight: bold;
          color: #4F46E5;
          margin-bottom: 10px;
          border-bottom: 2px solid #E5E7EB;
          padding-bottom: 5px;
        }
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
          margin-bottom: 20px;
        }
        .info-box {
          background-color: #F9FAFB;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #4F46E5;
        }
        .info-row {
          display: flex;
          margin-bottom: 8px;
        }
        .info-label {
          font-weight: bold;
          width: 120px;
          color: #374151;
        }
        .info-value {
          flex: 1;
          color: #111827;
        }
        .device-section {
          background-color: #F0F9FF;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 2px solid #0EA5E9;
        }
        .problem-section {
          background-color: #FEF3C7;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 2px solid #F59E0B;
        }
        .diagnosis-section {
          background-color: #DBEAFE;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 2px solid #3B82F6;
        }
        .repair-section {
          background-color: #D1FAE5;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 2px solid #10B981;
        }
        .cost-section {
          background-color: #F3F4F6;
          padding: 25px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 3px solid #4F46E5;
        }
        .cost-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;
          font-size: 13pt;
        }
        .cost-total {
          display: flex;
          justify-content: space-between;
          font-size: 18pt;
          font-weight: bold;
          color: #4F46E5;
          border-top: 3px solid #4F46E5;
          padding-top: 15px;
          margin-top: 20px;
        }
        .status-badge {
          display: inline-block;
          padding: 8px 16px;
          border-radius: 25px;
          font-size: 11pt;
          font-weight: bold;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        .status-diterima { background-color: #DBEAFE; color: #1E40AF; }
        .status-proses { background-color: #FEF3C7; color: #92400E; }
        .status-selesai-belum { background-color: #D1FAE5; color: #065F46; }
        .status-selesai-sudah { background-color: #E0E7FF; color: #3730A3; }
        .warranty-section {
          background-color: #FEE2E2;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 2px solid #EF4444;
        }
        .signature-section {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 40px;
          margin-top: 50px;
          margin-bottom: 30px;
        }
        .signature-box {
          text-align: center;
          padding: 20px;
          border: 2px dashed #D1D5DB;
          border-radius: 8px;
        }
        .signature-title {
          font-weight: bold;
          margin-bottom: 50px;
          color: #374151;
        }
        .signature-line {
          border-top: 2px solid #000;
          margin-top: 10px;
          padding-top: 8px;
          font-size: 10pt;
        }
        .footer {
          text-align: center;
          font-size: 10pt;
          color: #666;
          border-top: 2px solid #E5E7EB;
          padding-top: 20px;
          margin-top: 30px;
        }
        .priority-high { color: #DC2626; font-weight: bold; }
        .priority-medium { color: #D97706; font-weight: bold; }
        .priority-low { color: #059669; font-weight: bold; }
        @media print {
          .invoice-container {
            max-width: none;
            padding: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Header -->
        <div class="header">
          <div class="company-name">KivaPOS</div>
          <div class="company-tagline">Sistem Manajemen Servis Profesional</div>
          <div class="company-details">
            Email: <EMAIL> | Website: www.kivapos.id | Telepon: +62 xxx-xxxx-xxxx
          </div>
          <div class="invoice-title">FAKTUR SERVIS</div>
        </div>

        <!-- Invoice Meta -->
        <div class="invoice-meta">
          <div>
            <div class="invoice-number">No: ${service.serviceNumber}</div>
            <div class="invoice-date">Tanggal: ${new Date(service.receivedDate).toLocaleDateString("id-ID")}</div>
          </div>
          <div style="text-align: right;">
            <div style="margin-bottom: 5px;">Status:</div>
            <span class="status-badge status-${service.status.toLowerCase().replace(/_/g, "-")}">${service.status.replace(/_/g, " ")}</span>
          </div>
        </div>

        <!-- Customer and Service Info -->
        <div class="info-grid">
          <div class="info-box">
            <div class="section-title">Informasi Pelanggan</div>
            <div class="info-row">
              <div class="info-label">Nama:</div>
              <div class="info-value">${service.customerName}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Telepon:</div>
              <div class="info-value">${service.customerPhone}</div>
            </div>
            ${
              service.customerEmail
                ? `
            <div class="info-row">
              <div class="info-label">Email:</div>
              <div class="info-value">${service.customerEmail}</div>
            </div>
            `
                : ""
            }
          </div>
          
          <div class="info-box">
            <div class="section-title">Informasi Servis</div>
            <div class="info-row">
              <div class="info-label">Tanggal Masuk:</div>
              <div class="info-value">${new Date(service.receivedDate).toLocaleDateString("id-ID")}</div>
            </div>
            ${
              service.estimatedCompletionDate
                ? `
            <div class="info-row">
              <div class="info-label">Est. Selesai:</div>
              <div class="info-value">${new Date(service.estimatedCompletionDate).toLocaleDateString("id-ID")}</div>
            </div>
            `
                : ""
            }
            ${
              service.priorityLevel
                ? `
            <div class="info-row">
              <div class="info-label">Prioritas:</div>
              <div class="info-value priority-${service.priorityLevel.toLowerCase()}">${service.priorityLevel}</div>
            </div>
            `
                : ""
            }
          </div>
        </div>

        <!-- Device Information -->
        <div class="device-section">
          <div class="section-title">Informasi Perangkat</div>
          <div class="info-grid">
            <div>
              <div class="info-row">
                <div class="info-label">Jenis:</div>
                <div class="info-value">${service.deviceType}</div>
              </div>
              <div class="info-row">
                <div class="info-label">Merek:</div>
                <div class="info-value">${service.deviceBrand}</div>
              </div>
            </div>
            <div>
              <div class="info-row">
                <div class="info-label">Model:</div>
                <div class="info-value">${service.deviceModel}</div>
              </div>
              ${
                service.deviceSerialNumber
                  ? `
              <div class="info-row">
                <div class="info-label">Serial:</div>
                <div class="info-value">${service.deviceSerialNumber}</div>
              </div>
              `
                  : ""
              }
            </div>
          </div>
        </div>

        <!-- Problem Description -->
        <div class="problem-section">
          <div class="section-title">Keluhan/Masalah</div>
          <p style="margin: 0; font-size: 12pt; line-height: 1.6;">${service.problemDescription}</p>
        </div>

        ${
          service.diagnosisNotes
            ? `
        <!-- Diagnosis -->
        <div class="diagnosis-section">
          <div class="section-title">Diagnosis</div>
          <p style="margin: 0; font-size: 12pt; line-height: 1.6;">${service.diagnosisNotes}</p>
        </div>
        `
            : ""
        }

        ${
          service.repairNotes
            ? `
        <!-- Repair Notes -->
        <div class="repair-section">
          <div class="section-title">Catatan Perbaikan</div>
          <p style="margin: 0; font-size: 12pt; line-height: 1.6;">${service.repairNotes}</p>
        </div>
        `
            : ""
        }

        <!-- Cost Summary -->
        <div class="cost-section">
          <div class="section-title">Rincian Biaya</div>
          ${
            service.estimatedCost
              ? `
          <div class="cost-row">
            <span>Estimasi Biaya:</span>
            <span>Rp ${Number(service.estimatedCost).toLocaleString("id-ID")}</span>
          </div>
          `
              : ""
          }
          <div class="cost-total">
            <span>Total Biaya Servis:</span>
            <span>Rp ${costNumber.toLocaleString("id-ID")}</span>
          </div>
        </div>

        ${
          service.warrantyPeriod && service.warrantyPeriod > 0
            ? `
        <!-- Warranty Information -->
        <div class="warranty-section">
          <div class="section-title">Informasi Garansi</div>
          <p style="margin: 0 0 10px 0; font-weight: bold;">Periode Garansi: ${service.warrantyPeriod} hari</p>
          <p style="margin: 0; font-size: 10pt; font-style: italic;">
            Garansi berlaku untuk kerusakan yang sama dan tidak mencakup kerusakan akibat pemakaian yang tidak wajar, 
            jatuh, terkena air, atau force majeure.
          </p>
        </div>
        `
            : ""
        }

        <!-- Signature Section -->
        <div class="signature-section">
          <div class="signature-box">
            <div class="signature-title">Teknisi</div>
            <div class="signature-line">(_________________)</div>
          </div>
          <div class="signature-box">
            <div class="signature-title">Pelanggan</div>
            <div class="signature-line">(_________________)</div>
          </div>
        </div>

        <!-- Footer -->
        <div class="footer">
          <p><strong>Terima kasih telah mempercayakan servis perangkat Anda kepada kami.</strong></p>
          <p>Faktur ini dicetak secara otomatis oleh sistem KivaPOS pada ${new Date().toLocaleString("id-ID")}</p>
          <p style="font-size: 9pt; margin-top: 10px;">
            Untuk pertanyaan atau keluhan, silakan hubungi customer service kami.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
};
