"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { ThemeToggle } from "@/components/theme-toggle";
import Image from "next/image";

export const LoginNavbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  return (
    <nav className="w-full text-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2">
            <div className="relative w-[150px] h-[38px]">
              {/* Light mode logo */}
              <Image
                src="/images/kivapos-4.svg"
                alt="KivaPOS Logo"
                fill
                className="object-contain dark:hidden"
                priority
              />

              {/* Dark mode logo */}
              <Image
                src="/images/kivapos-3.svg"
                alt="KivaPOS Logo Dark"
                fill
                className="object-contain hidden dark:block"
                priority
              />
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="flex items-center gap-4">
            <Button asChild variant="default">
              <Link href="/login">Login</Link>
            </Button>

            <Button asChild variant="default">
              <Link href="/register">Registrasi</Link>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};
