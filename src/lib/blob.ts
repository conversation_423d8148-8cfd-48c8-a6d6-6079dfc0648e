import { uploadToS3, deleteFromS3 } from "./s3";

// Upload a file to AWS S3 (replacing Vercel Blob)
export async function uploadToBlob(file: File, userId: string) {
  try {
    // Upload the file to AWS S3
    const result = await uploadToS3(file, userId);

    return result;
  } catch (error) {
    console.error("Error uploading to S3:", error);
    return { error: "Failed to upload image", success: false };
  }
}

// Delete a file from AWS S3 (replacing Vercel Blob)
export async function deleteFromBlob(url: string) {
  try {
    const result = await deleteFromS3(url);
    return result;
  } catch (error) {
    console.error("Error deleting from S3:", error);
    return { error: "Failed to delete image", success: false };
  }
}
