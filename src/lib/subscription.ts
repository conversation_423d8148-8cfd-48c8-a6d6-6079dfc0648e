import { db } from "@/lib/prisma";
import { SubscriptionPlan, PaymentStatus } from "@prisma/client";
// COMMENTED OUT - XENDIT IMPLEMENTATION (KEPT FOR REFERENCE)
// import { createInvoice } from "@/lib/xendit";
import { createTransaction } from "@/lib/midtrans";
import { addMonths, addYears } from "date-fns";
import { calculateAnnualPrice } from "./discount-config";

// Define subscription plan details with comprehensive limits
export const SUBSCRIPTION_PLANS = {
  BASIC: {
    name: "Paket Dasar",
    price: 99000,
    period: "per bulan",
    description:
      "Untuk bisnis kecil hingga menengah (30 hari gratis untuk pengguna baru)",
    features: [
      "Maksimal 5 Produk",
      "Maksimal 100 Trx/bulan",
      "2 Pengguna",
      "50 Supplier",
      "300 Customer",
      "Notifikasi Website",
      "Backup Manual Bulanan",
    ],
    limitations: ["Tidak ada notifikasi email", "Tidak ada backup otomatis"],
    limits: {
      maxProducts: 5,
      maxTransactionsPerMonth: 100,
      maxUsers: 2, // Owner + 1 employee
      maxSuppliers: 50, // Separate limit for suppliers
      maxCustomers: 300, // Separate limit for customers
      maxContacts: 350, // Legacy field for backward compatibility (maxSuppliers + maxCustomers)
      notifications: {
        website: true,
        email: false,
      },
      backup: {
        manual: true,
        automatic: false,
        frequencies: ["monthly"],
      },
      support: {
        priority: false,
        developer: false,
      },
    },
    durationMonths: 1,
  },
  PRO: {
    name: "Paket Pro",
    price: 199000,
    period: "per bulan",
    description: "Untuk bisnis menengah hingga besar",
    features: [
      "Maksimal 500 Produk",
      "Maksimal 10.000 Trx/bulan",
      "100 Pengguna",
      "100 Supplier",
      "1.000 Customer",
      "Notifikasi Website & Email",
      "Backup Manual: Harian, Bulanan, Tahunan",
    ],
    limitations: ["Tidak ada backup otomatis"],
    limits: {
      maxProducts: 500,
      maxTransactionsPerMonth: 10000,
      maxUsers: 100, // Owner + 99 employees
      maxSuppliers: 100, // Separate limit for suppliers
      maxCustomers: 1000, // Separate limit for customers
      maxContacts: 1100, // Legacy field for backward compatibility (maxSuppliers + maxCustomers)
      notifications: {
        website: true,
        email: true,
      },
      backup: {
        manual: true,
        automatic: false,
        frequencies: ["daily", "monthly", "yearly"],
      },
      support: {
        priority: true,
        developer: false,
      },
    },
    durationMonths: 1,
  },
  ENTERPRISE: {
    name: "Paket Enterprise",
    price: 499000,
    period: "per bulan",
    description: "Untuk perusahaan besar dengan kebutuhan khusus",
    features: [
      "Unlimited Produk",
      "Unlimited Transaksi",
      "Unlimited Pengguna",
      "Unlimited Supplier",
      "Unlimited Customer",
      "Notifikasi Website + Email",
      "Backup Otomatis: Harian, Bulanan, Tahunan",
      "Dukungan Prioritas Developer",
    ],
    limitations: [],
    limits: {
      maxProducts: null, // Unlimited
      maxTransactionsPerMonth: null, // Unlimited
      maxUsers: null, // Unlimited
      maxSuppliers: null, // Unlimited suppliers
      maxCustomers: null, // Unlimited customers
      maxContacts: null, // Legacy field for backward compatibility
      notifications: {
        website: true,
        email: true,
      },
      backup: {
        manual: true,
        automatic: true,
        frequencies: ["daily", "monthly", "yearly"],
      },
      support: {
        priority: true,
        developer: true,
      },
    },
    durationMonths: 1,
  },
};

// Annual subscription plans with discount
export const ANNUAL_SUBSCRIPTION_PLANS = {
  BASIC: {
    ...SUBSCRIPTION_PLANS.BASIC,
    name: "Paket Dasar (Tahunan)",
    period: "per tahun",
    durationMonths: 12,
    ...(() => {
      const annualPricing = calculateAnnualPrice(
        SUBSCRIPTION_PLANS.BASIC.price
      );
      return {
        price: annualPricing.discountedAnnualPrice,
        originalPrice: annualPricing.originalAnnualPrice,
        savings: annualPricing.savings,
        discountPercentage: annualPricing.discountPercentage,
      };
    })(),
    description:
      "Untuk bisnis kecil hingga menengah (Hemat 10% dengan berlangganan tahunan)",
  },
  PRO: {
    ...SUBSCRIPTION_PLANS.PRO,
    name: "Paket Pro (Tahunan)",
    period: "per tahun",
    durationMonths: 12,
    ...(() => {
      const annualPricing = calculateAnnualPrice(SUBSCRIPTION_PLANS.PRO.price);
      return {
        price: annualPricing.discountedAnnualPrice,
        originalPrice: annualPricing.originalAnnualPrice,
        savings: annualPricing.savings,
        discountPercentage: annualPricing.discountPercentage,
      };
    })(),
    description:
      "Untuk bisnis menengah hingga besar (Hemat 10% dengan berlangganan tahunan)",
  },
  ENTERPRISE: {
    ...SUBSCRIPTION_PLANS.ENTERPRISE,
    name: "Paket Enterprise (Tahunan)",
    period: "per tahun",
    durationMonths: 12,
    ...(() => {
      const annualPricing = calculateAnnualPrice(
        SUBSCRIPTION_PLANS.ENTERPRISE.price
      );
      return {
        price: annualPricing.discountedAnnualPrice,
        originalPrice: annualPricing.originalAnnualPrice,
        savings: annualPricing.savings,
        discountPercentage: annualPricing.discountPercentage,
      };
    })(),
    description:
      "Untuk perusahaan besar dengan kebutuhan khusus (Hemat 10% dengan berlangganan tahunan)",
  },
};

// Get subscription plan details
export function getPlanDetails(plan: SubscriptionPlan) {
  return SUBSCRIPTION_PLANS[plan];
}

// Get annual subscription plan details
export function getAnnualPlanDetails(plan: SubscriptionPlan) {
  return ANNUAL_SUBSCRIPTION_PLANS[plan];
}

// Trial period constants
export const TRIAL_DURATION_DAYS = 30;

// Check if user is in trial period
export function isUserInTrial(user: {
  trialStartDate?: Date | null;
  trialEndDate?: Date | null;
  isTrialActive?: boolean | null;
}) {
  if (!user.isTrialActive || !user.trialStartDate || !user.trialEndDate) {
    return false;
  }

  const now = new Date();
  return now >= user.trialStartDate && now <= user.trialEndDate;
}

// Calculate trial end date
export function calculateTrialEndDate(startDate: Date = new Date()): Date {
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + TRIAL_DURATION_DAYS);
  return endDate;
}

// Get effective plan (considering trial status)
export function getEffectivePlan(user: {
  currentPlan: SubscriptionPlan;
  trialStartDate?: Date | null;
  trialEndDate?: Date | null;
  isTrialActive?: boolean | null;
}): SubscriptionPlan {
  // If user is in trial, they get BASIC plan features
  if (isUserInTrial(user)) {
    return "BASIC";
  }

  return user.currentPlan;
}

// Create a new subscription
export async function createSubscription(
  userId: string,
  plan: SubscriptionPlan,
  isAnnual: boolean = false
) {
  const planDetails = isAnnual
    ? getAnnualPlanDetails(plan)
    : getPlanDetails(plan);

  if (!planDetails) {
    throw new Error("Invalid subscription plan");
  }

  // Calculate end date based on plan duration
  const endDate = planDetails.durationMonths
    ? isAnnual
      ? addYears(new Date(), 1)
      : addMonths(new Date(), planDetails.durationMonths)
    : null;

  try {
    // Create subscription in database
    const subscription = await db.subscription.create({
      data: {
        userId,
        plan,
        endDate: endDate || new Date(2099, 11, 31),
        autoRenew: true, // Auto-renew for all plans
      },
    });

    // For paid plans, create a payment record
    if (planDetails.price > 0) {
      const externalId = `sub_${subscription.id}_${Date.now()}`;

      // Create payment record in database
      const payment = await db.payment.create({
        data: {
          userId,
          subscriptionId: subscription.id,
          amount: planDetails.price,
          status: PaymentStatus.PENDING,
          externalId,
        },
      });

      // Create Midtrans transaction
      console.log("🚀 [SUBSCRIPTION] Creating Midtrans transaction for user:", {
        userId,
        plan,
        amount: planDetails.price,
        externalId,
      });

      const user = await db.user.findUnique({
        where: { id: userId },
        select: { name: true, email: true, phone: true },
      });

      console.log("👤 [SUBSCRIPTION] User data retrieved:", {
        userId,
        hasName: !!user?.name,
        hasEmail: !!user?.email,
        hasPhone: !!user?.phone,
      });

      // Prepare customer data for Midtrans
      const customerData = user
        ? {
            email: user.email || undefined,
            first_name: user.name?.split(" ")[0] || undefined,
            last_name: user.name?.split(" ").slice(1).join(" ") || undefined,
            phone: user.phone || undefined,
          }
        : undefined;

      const transactionResult = await createTransaction({
        orderId: externalId,
        amount: planDetails.price,
        description: `Langganan ${planDetails.name} - ${planDetails.durationMonths} bulan`,
        customer: customerData,
        items: [
          {
            id: `plan-${plan.toLowerCase()}`,
            name: `Langganan ${planDetails.name}`,
            quantity: 1,
            price: planDetails.price,
            category: "Subscription Plan",
          },
        ],
        successRedirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/billing/success?payment_id=${payment.id}`,
        failureRedirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/billing/failed?payment_id=${payment.id}`,
        userId: userId,
        planName: plan, // This will be used for the enhanced order ID format
      });

      if (transactionResult.success && transactionResult.data) {
        console.log(
          "✅ [SUBSCRIPTION] Midtrans transaction created successfully:",
          {
            orderId: transactionResult.data.order_id,
            hasToken: !!transactionResult.data.token,
            hasRedirectUrl: !!transactionResult.data.redirect_url,
          }
        );

        // Update payment record with Midtrans transaction details
        await db.payment.update({
          where: { id: payment.id },
          data: {
            invoiceId: transactionResult.data.order_id,
            externalUrl: transactionResult.data.redirect_url,
            // Midtrans doesn't provide expiry date in response, set default 24 hours
            expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
            metadata: {
              midtransToken: transactionResult.data.token,
              midtransOrderId: transactionResult.data.order_id,
              originalOrderId: transactionResult.data.original_order_id,
              planName: plan,
              planDetails: planDetails,
            },
          },
        });

        console.log(
          "✅ [SUBSCRIPTION] Payment record updated with Midtrans details:",
          {
            paymentId: payment.id,
            orderId: transactionResult.data.order_id,
          }
        );

        return {
          subscription,
          payment,
          invoiceUrl: transactionResult.data.redirect_url,
          snapToken: transactionResult.data.token, // For Snap integration
        };
      } else {
        console.error(
          "❌ [SUBSCRIPTION] Failed to create Midtrans transaction:",
          {
            error: transactionResult.error,
            details: transactionResult.details,
          }
        );
        throw new Error(
          `Failed to create payment transaction: ${transactionResult.error}`
        );
      }
    }

    return { subscription };
  } catch (error) {
    console.error("Error creating subscription:", error);
    throw error;
  }
}

// Update user's subscription status based on payment
export async function updateSubscriptionStatus(
  paymentId: string,
  status: PaymentStatus
) {
  try {
    const payment = await db.payment.findUnique({
      where: { id: paymentId },
      include: { subscription: true },
    });

    if (!payment || !payment.subscription) {
      throw new Error("Payment or subscription not found");
    }

    // Update payment status
    await db.payment.update({
      where: { id: paymentId },
      data: {
        status,
        paymentDate: status === PaymentStatus.COMPLETED ? new Date() : null,
      },
    });

    // If payment is completed, update user's subscription
    if (status === PaymentStatus.COMPLETED) {
      await db.user.update({
        where: { id: payment.userId },
        data: {
          currentPlan: payment.subscription.plan,
          subscriptionExpiry: payment.subscription.endDate,
        },
      });

      // Update subscription status to active
      await db.subscription.update({
        where: { id: payment.subscription.id },
        data: { status: "active" },
      });
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating subscription status:", error);
    return { success: false, error };
  }
}

// Cancel a subscription
export async function cancelSubscription(subscriptionId: string) {
  try {
    // Update subscription status to canceled
    await db.subscription.update({
      where: { id: subscriptionId },
      data: { status: "canceled", autoRenew: false },
    });

    return { success: true };
  } catch (error) {
    console.error("Error canceling subscription:", error);
    return { success: false, error };
  }
}

// Get user's active subscription
export async function getUserSubscription(userId: string) {
  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        currentPlan: true,
        subscriptionExpiry: true,
      },
    });

    if (!user) {
      throw new Error("User not found");
    }

    const planDetails = getPlanDetails(user.currentPlan);

    return {
      plan: user.currentPlan,
      planDetails,
      expiryDate: user.subscriptionExpiry,
      isActive: user.subscriptionExpiry
        ? new Date(user.subscriptionExpiry) > new Date()
        : true,
    };
  } catch (error) {
    console.error("Error getting user subscription:", error);
    throw error;
  }
}

// Get user's payment history
export async function getUserPaymentHistory(userId: string) {
  try {
    const payments = await db.payment.findMany({
      where: { userId },
      orderBy: { createdAt: "desc" },
      include: { subscription: true },
    });

    return payments;
  } catch (error) {
    console.error("Error getting payment history:", error);
    throw error;
  }
}

// Check if a payment is completed
export async function isPaymentCompleted(paymentId: string) {
  try {
    const payment = await db.payment.findUnique({
      where: { id: paymentId },
    });

    return payment?.status === PaymentStatus.COMPLETED;
  } catch (error) {
    console.error("Error checking payment status:", error);
    return false;
  }
}

/**
 * Get usage percentage for a specific limit
 */
export function getUsagePercentage(
  current: number,
  limit: number | null
): number {
  if (limit === null) return 0; // Unlimited
  if (limit === 0) return 100; // No limit allowed
  return Math.min((current / limit) * 100, 100);
}

/**
 * Check if usage is approaching limit (>80%)
 */
export function isApproachingLimit(
  current: number,
  limit: number | null
): boolean {
  if (limit === null) return false; // Unlimited
  return getUsagePercentage(current, limit) > 80;
}

/**
 * Check if usage has exceeded limit
 */
export function hasExceededLimit(
  current: number,
  limit: number | null
): boolean {
  if (limit === null) return false; // Unlimited
  return current > limit;
}
