// Purchase import template generation utilities
// Creates standardized Excel import templates for purchase transactions

import * as XLSX from "xlsx-js-style";
import {
  applyCellStyle,
  mergeCells,
  setColumnWidths,
  setRowHeights,
  applyHeaderStyling,
  applyColumnHeaderStyling,
  setStandardRowHeights,
  createInstructionsSheet,
  getCommonInstructions,
  getDateFormatInstructions,
  CELL_STYLES,
  BRAND_COLORS,
} from "./shared";

/**
 * Creates a professional import template for purchases
 */
export const createPurchaseImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create main template sheet
  const templateData = [
    // Header rows
    ["TEMPLATE IMPORT PEMBELIAN", "", "", "", "", "", "", "", "", ""],
    [
      "KivaPOS - Sistem Manajemen Pembelian",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
    ],
    ["", "", "", "", "", "", "", "", "", ""],
    // Column headers (exactly matching export format)
    [
      "Tanggal Pembelian",
      "Nama Supplier",
      "Nomor Telepon",
      "Email Supplier",
      "Nama Produk",
      "Quantity",
      "Harga Beli",
      "Diskon (%)",
      "Diskon (Rp)",
      "PPN (%)",
      "Status Pembayaran",
      "Memo",
      "No. Invoice",
      "Satuan",
    ],
    // Sample data rows
    [
      "2024-01-15",
      "PT Supplier ABC",
      "021-12345678",
      "<EMAIL>",
      "Produk A",
      "10",
      "45000",
      "5",
      "2000",
      "11",
      "Belum Lunas",
      "Pembelian rutin",
      "INV-001",
      "Pcs",
    ],
    [
      "2024-01-15",
      "CV Supplier XYZ",
      "021-87654321",
      "<EMAIL>",
      "Produk B",
      "5",
      "65000",
      "0",
      "0",
      "11",
      "Lunas",
      "",
      "INV-002",
      "Pcs",
    ],
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styling
  applyHeaderStyling(
    templateSheet,
    "TEMPLATE IMPORT PEMBELIAN",
    "KivaPOS - Sistem Manajemen Pembelian",
    10
  );

  // Style column headers
  applyColumnHeaderStyling(templateSheet, 10);

  // Set column widths
  setColumnWidths(templateSheet, [
    { wch: 15 }, // Tanggal Pembelian
    { wch: 20 }, // Nama Supplier
    { wch: 15 }, // Nomor Telepon
    { wch: 25 }, // Email Supplier
    { wch: 25 }, // Nama Produk
    { wch: 10 }, // Quantity
    { wch: 12 }, // Harga Beli
    { wch: 10 }, // Diskon (%)
    { wch: 12 }, // Diskon (Rp)
    { wch: 10 }, // PPN (%)
    { wch: 15 }, // Status Pembayaran
    { wch: 20 }, // Memo
    { wch: 15 }, // No. Invoice
    { wch: 10 }, // Satuan
  ]);

  // Set row heights
  setStandardRowHeights(templateSheet);

  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Pembelian");

  // Create instructions sheet
  const instructions = [
    "1. KOLOM WAJIB (Harus diisi):",
    "   • Tanggal Pembelian: Format YYYY-MM-DD (contoh: 2024-01-15)",
    "   • Nama Produk: Nama produk yang dibeli (harus sudah ada di sistem)",
    "   • Quantity: Jumlah produk yang dibeli",
    "   • Harga Satuan: Harga per unit (dalam Rupiah, tanpa titik/koma)",
    "",
    "2. KOLOM OPSIONAL:",
    "   • Nama Supplier: Nama pemasok",
    "   • Telepon Supplier: Nomor telepon pemasok",
    "   • Email Supplier: Email pemasok",
    "   • Diskon: Jumlah diskon dalam Rupiah",
    "   • PPN (%): Persentase PPN (contoh: 11 untuk 11%)",
    "   • Catatan: Catatan tambahan untuk pembelian",
    "",
    "3. FORMAT DATA:",
    ...getDateFormatInstructions(),
    "   • PPN: Masukkan persentase (contoh: 11)",
    "",
    "4. TIPS IMPORT:",
    "   • Produk harus sudah ada di sistem sebelum import",
    "   • Supplier akan dibuat otomatis jika belum ada",
    "   • Hapus baris contoh sebelum import",
    "   • Pastikan tidak ada baris kosong di tengah data",
    "",
    "5. TROUBLESHOOTING:",
    "   • Jika import gagal, periksa format tanggal",
    "   • Pastikan produk sudah ada di sistem",
    "   • Periksa format angka (tanpa titik/koma)",
  ];

  createInstructionsSheet(
    workbook,
    "PETUNJUK PENGGUNAAN TEMPLATE IMPORT PEMBELIAN",
    instructions
  );

  return workbook;
};
