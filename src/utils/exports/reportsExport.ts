// Professional Excel export functionality for Reports (Summary and Income Statement)
// Extracted from consolidated excelTemplate.ts for better maintainability

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
  BRAND_COLORS,
} from "../excelStyles";

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const formatDate = (date: string | Date): string => {
  try {
    return new Date(date).toLocaleDateString("id-ID");
  } catch (error) {
    return String(date);
  }
};

// --- REPORT DATA INTERFACE ---

export interface ReportData {
  sales?: any[];
  purchases?: any[];
  products?: any[];
  customers?: any[];
  suppliers?: any[];
  incomeStatement?: any[];
  services?: any[];
  spareParts?: any[];
  summary?: {
    totalSales?: number;
    totalPurchases?: number;
    totalProducts?: number;
    totalCustomers?: number;
    totalSuppliers?: number;
    period?: string;
    generatedAt?: Date;
  };
  filters?: {
    dateRange?: string;
    startDate?: Date;
    endDate?: Date;
    category?: string;
    supplier?: string;
    customer?: string;
    status?: string;
  };
  reportType?: "harian" | "bulanan" | "tahunan";
}

export interface ExcelTemplateOptions {
  companyName?: string;
  reportTitle?: string;
  includeCharts?: boolean;
  includeSummary?: boolean;
  autoFitColumns?: boolean;
}

// --- SUMMARY SHEET FUNCTIONS ---

/**
 * Creates the main summary sheet for the report.
 */
export const createSummarySheet = (
  data: ReportData,
  options: ExcelTemplateOptions
): XLSX.WorkSheet => {
  const companyName = options.companyName || "KivaPOS";
  const reportTitle = options.reportTitle || "Laporan Keuangan";
  const wsData = [
    [companyName],
    ["Sistem Manajemen Kasir Profesional"],
    [null],
    [reportTitle],
    [null],
    ["Informasi Laporan"],
    [
      "Jenis Laporan:",
      data.reportType
        ? data.reportType.charAt(0).toUpperCase() + data.reportType.slice(1)
        : "Kustom",
    ],
    ["Periode:", data.summary?.period || "Tidak ditentukan"],
    ["Tanggal Export:", new Date().toLocaleDateString("id-ID")],
    [null],
    ["Filter yang Diterapkan"],
    ["Rentang Tanggal:", data.filters?.dateRange || "Semua"],
    [
      "Tanggal Mulai:",
      data.filters?.startDate ? formatDate(data.filters.startDate) : "N/A",
    ],
    [
      "Tanggal Akhir:",
      data.filters?.endDate ? formatDate(data.filters.endDate) : "N/A",
    ],
    ["Kategori:", data.filters?.category || "Semua"],
    [null],
    ["Ringkasan Data"],
    [
      "Total Penjualan:",
      {
        t: "n",
        v: data.summary?.totalSales || 0,
        s: { ...CELL_STYLES.summary, numFmt: NUMBER_FORMATS.currency },
      },
    ],
    [
      "Total Pembelian:",
      {
        t: "n",
        v: data.summary?.totalPurchases || 0,
        s: { ...CELL_STYLES.summary, numFmt: NUMBER_FORMATS.currency },
      },
    ],
    [
      "Total Produk:",
      {
        t: "n",
        v: data.summary?.totalProducts || 0,
        s: { ...CELL_STYLES.summary, numFmt: NUMBER_FORMATS.integer },
      },
    ],
  ];
  const worksheet = XLSX.utils.aoa_to_sheet(wsData);

  // Styling and Merging
  applyCellStyle(worksheet, "A1", CELL_STYLES.reportTitle);
  mergeCells(worksheet, "A1:B1");
  applyCellStyle(worksheet, "A2", CELL_STYLES.info);
  mergeCells(worksheet, "A2:B2");
  applyCellStyle(worksheet, "A4", CELL_STYLES.sectionHeader);
  mergeCells(worksheet, "A4:B4");

  const sections = [6, 11, 17];
  sections.forEach((row) => {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.sectionHeader);
    mergeCells(worksheet, `A${row}:B${row}`);
  });

  for (let r = 7; r <= 15; r++) {
    if (worksheet[`A${r}`])
      applyCellStyle(worksheet, `A${r}`, CELL_STYLES.info);
    if (worksheet[`B${r}`])
      applyCellStyle(worksheet, `B${r}`, CELL_STYLES.tableDataEven);
  }
  for (let r = 18; r <= 20; r++) {
    if (worksheet[`A${r}`])
      applyCellStyle(worksheet, `A${r}`, CELL_STYLES.info);
  }

  setColumnWidths(worksheet, [{ wch: 25 }, { wch: 30 }]);
  setRowHeights(worksheet, { 1: 30, 4: 25 });
  return worksheet;
};

// --- INCOME STATEMENT FUNCTIONS ---

/**
 * Apply special formatting for income statement sheet
 */
export const applyIncomeStatementFormatting = (
  worksheet: XLSX.WorkSheet,
  data: any[]
): void => {
  const headerRowCount = 4; // Title, subtitle, spacer, headers

  data.forEach((item, index) => {
    const rowIndex = headerRowCount + index + 1;

    // Apply special formatting for subtotals and totals
    if (item.isSubTotal || item.isTotal) {
      // Bold formatting for subtotals and totals
      const cellA = `A${rowIndex}`;
      const cellB = `B${rowIndex}`;
      const cellC = `C${rowIndex}`;

      applyCellStyle(worksheet, cellA, {
        ...CELL_STYLES.tableDataEven,
        font: { ...CELL_STYLES.tableDataEven.font, bold: true },
        fill: {
          patternType: "solid",
          fgColor: {
            rgb: item.isTotal ? BRAND_COLORS.primary : BRAND_COLORS.gray[100],
          },
        },
      });

      applyCellStyle(worksheet, cellB, {
        ...CELL_STYLES.tableDataEven,
        font: { ...CELL_STYLES.tableDataEven.font, bold: true },
        fill: {
          patternType: "solid",
          fgColor: {
            rgb: item.isTotal ? BRAND_COLORS.primary : BRAND_COLORS.gray[100],
          },
        },
      });

      applyCellStyle(worksheet, cellC, {
        ...CELL_STYLES.tableDataEven,
        font: {
          ...CELL_STYLES.tableDataEven.font,
          bold: true,
          color: item.isTotal ? { rgb: "FFFFFF" } : undefined,
        },
        fill: {
          patternType: "solid",
          fgColor: {
            rgb: item.isTotal ? BRAND_COLORS.primary : BRAND_COLORS.gray[100],
          },
        },
        numFmt: NUMBER_FORMATS.currency,
      });

      // Add borders for totals
      if (item.isTotal) {
        const borderStyle = {
          style: "thick",
          color: { rgb: BRAND_COLORS.primary },
        };
        [cellA, cellB, cellC].forEach((cell) => {
          const currentStyle = worksheet[cell]?.s || {};
          applyCellStyle(worksheet, cell, {
            ...currentStyle,
            border: {
              top: borderStyle,
              bottom: borderStyle,
              left: borderStyle,
              right: borderStyle,
            },
          });
        });
      }
    }
  });
};

/**
 * Creates an income statement data sheet
 */
export const createIncomeStatementSheet = (
  data: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Laporan Laba Rugi";
  const columns = [
    { key: "category", label: "Kategori", type: "text" as const },
    { key: "item", label: "Item", type: "text" as const },
    { key: "amount", label: "Jumlah", type: "currency" as const },
  ];

  const headerRowCount = 4;
  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 2. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  const headerStyle = CELL_STYLES.tableHeader;
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      headerStyle
    );
  });

  // 3. Process and Add Data Rows
  const rows = data.map((item) =>
    columns.map((col) => {
      let value = item[col.key];
      if (value === null || value === undefined) return "";

      switch (col.type) {
        case "currency":
          return typeof value === "number" ? value : 0;
        default:
          return String(value);
      }
    })
  );

  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 4. Style Data Rows
  rows.forEach((_, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    const isEven = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style: ExcelCellStyle = JSON.parse(
        JSON.stringify(
          isEven ? CELL_STYLES.tableDataEven : CELL_STYLES.tableDataOdd
        )
      );

      if (col.type === "currency") {
        style.numFmt = NUMBER_FORMATS.currency;
        if (style.alignment) style.alignment.horizontal = "right";
      }
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 5. Apply special income statement formatting
  applyIncomeStatementFormatting(worksheet, data);

  // 6. Set column widths and row heights
  setColumnWidths(worksheet, [{ wch: 20 }, { wch: 30 }, { wch: 20 }]);
  setRowHeights(worksheet, { 1: 22, 2: 18, [headerRowCount]: 30 });

  return worksheet;
};

/**
 * Creates a complete reports Excel workbook with summary and income statement
 */
export const createReportsExcelWorkbook = (
  data: ReportData,
  options: ExcelTemplateOptions = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const reportPeriod = data.summary?.period || "Tidak Ditentukan";

  // 1. Create Summary Sheet
  if (options.includeSummary !== false) {
    const summarySheet = createSummarySheet(data, options);
    XLSX.utils.book_append_sheet(workbook, summarySheet, "📊 Ringkasan");
  }

  // 2. Income Statement Sheet (if data exists)
  if (data.incomeStatement && data.incomeStatement.length > 0) {
    const incomeStatementSheet = createIncomeStatementSheet(
      data.incomeStatement,
      reportPeriod
    );
    XLSX.utils.book_append_sheet(
      workbook,
      incomeStatementSheet,
      "💰 Laba Rugi"
    );
  }

  return workbook;
};
