// Professional Excel export functionality for Purchases, with single-sheet support, transaction borders, and cell merging.

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
} from "../excelStyles";

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- NEW MERGE LOGIC ---
const applyMergesAndVerticalAlign = (
  worksheet: XLSX.WorkSheet,
  data: any[],
  columns: any[],
  headerRowCount: number
) => {
  const mergeableColumns = columns
    .map((col, index) => ({ ...col, index }))
    .filter((col) => !col.key.startsWith("item."));

  if (data.length === 0) return;

  // Apply vertical alignment to all mergeable columns
  data.forEach((_, rowIndex) => {
    mergeableColumns.forEach((col) => {
      const cellRef = XLSX.utils.encode_cell({
        r: rowIndex + headerRowCount,
        c: col.index,
      });
      if (worksheet[cellRef]) {
        if (!worksheet[cellRef].s) worksheet[cellRef].s = {};
        if (!worksheet[cellRef].s.alignment)
          worksheet[cellRef].s.alignment = {};
        worksheet[cellRef].s.alignment.vertical = "center";
      }
    });
  });

  // Apply merges
  let mergeStartRow = 0;
  for (let i = 1; i <= data.length; i++) {
    if (
      i === data.length ||
      data[i].transactionNumber !== data[i - 1].transactionNumber
    ) {
      if (i - mergeStartRow > 1) {
        mergeableColumns.forEach((col) => {
          const start = { r: mergeStartRow + headerRowCount, c: col.index };
          const end = { r: i - 1 + headerRowCount, c: col.index };
          if (!worksheet["!merges"]) worksheet["!merges"] = [];
          worksheet["!merges"].push({ s: start, e: end });
        });
      }
      mergeStartRow = i;
    }
  }
};

// --- PURCHASES EXPORT ---

const createCombinedPurchasesSheet = (
  purchasesData: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Laporan Rinci Pembelian";
  const headerRowCount = 4;

  const columns = [
    // Purchase Details (Mergeable)
    { key: "transactionNumber", label: "No. Transaksi", type: "text" },
    { key: "purchaseDate", label: "Tanggal Pembelian", type: "date" },
    { key: "supplier.name", label: "Nama Supplier", type: "text" },
    { key: "supplier.phone", label: "Nomor Telepon", type: "text" },
    { key: "totalAmount", label: "Total Pembelian", type: "currency" },
    { key: "paymentDueDate", label: "Jatuh Tempo", type: "date" },
    {
      key: "status",
      label: "Status Pembayaran",
      type: "text",
      formatter: (value: string) =>
        value === "LUNAS" ? "Lunas" : "Belum Lunas",
    },
    { key: "memo", label: "Memo", type: "text" },
    {
      key: "isDraft",
      label: "Status Draft",
      type: "text",
      formatter: (isDraft: boolean) => (isDraft ? "Draft" : "Selesai"),
    },
    // Item Details (Not Mergeable)
    { key: "item.product.name", label: "Nama Produk", type: "text" },
    { key: "item.product.sku", label: "SKU", type: "text" },
    { key: "item.quantity", label: "Quantity", type: "number" },
    { key: "item.costAtPurchase", label: "Harga Beli", type: "currency" },
    { key: "item.discountPercentage", label: "Diskon (%)", type: "number" },
    { key: "item.discountAmount", label: "Diskon (Rp)", type: "currency" },
    { key: "item.unit", label: "Satuan", type: "text" },
  ];

  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Flatten data
  const processedData = purchasesData.flatMap((purchase) =>
    (purchase.items && purchase.items.length > 0 ? purchase.items : [{}]).map(
      (item: any) => ({
        // Ensure at least one row per purchase
        ...purchase,
        item: { ...item, product: item.product || {} },
      })
    )
  );

  // Add grouping flags for borders
  processedData.forEach((row, index, arr) => {
    row.isFirstInGroup =
      index === 0 || row.transactionNumber !== arr[index - 1].transactionNumber;
    row.isLastInGroup =
      index === arr.length - 1 ||
      row.transactionNumber !== arr[index + 1].transactionNumber;
  });

  // 2. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 3. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      SHEET_HEADER_STYLES.purchases
    );
  });

  // 4. Process and Add Data Rows
  const rows = processedData.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";
      if (col.formatter) value = (col.formatter as (value: any) => any)(value);
      switch (col.type) {
        case "currency":
        case "number":
          return typeof value === "number" ? value : 0;
        case "date":
          return value ? new Date(value) : "";
        default:
          return String(value);
      }
    })
  );
  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 5. Style Data Rows with Borders
  processedData.forEach((item, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style = JSON.parse(
        JSON.stringify(
          rowIndex % 2 === 0
            ? CELL_STYLES.tableDataEven
            : CELL_STYLES.tableDataOdd
        )
      );
      const border = { style: "thin", color: { rgb: "888888" } };
      if (item.isFirstInGroup) style.border = { ...style.border, top: border };
      if (item.isLastInGroup)
        style.border = { ...style.border, bottom: border };
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 6. Apply Merges and Final Styling
  applyMergesAndVerticalAlign(
    worksheet,
    processedData,
    columns,
    headerRowCount
  );

  // 7. Finalize sheet layout
  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      processedData.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    return {
      wch: Math.min(60, Math.max(headerLength + 5, maxDataLength + 3, 18)),
    };
  });
  setColumnWidths(worksheet, colWidths);
  setRowHeights(worksheet, { 1: 22, 2: 18, [headerRowCount]: 30 });
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (processedData.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${processedData.length + headerRowCount}`
    );
  }

  return worksheet;
};

// Create a simple import-friendly export format
export const createPurchasesImportFriendlyWorksheet = (
  purchasesData: any[]
): XLSX.WorkSheet => {
  console.log("[Export] Creating import-friendly purchases worksheet");

  // Simple column structure that matches import expectations
  const columns = [
    "Tanggal Pembelian",
    "Nama Supplier",
    "Nomor Telepon",
    "Email Supplier",
    "Nama Produk",
    "Quantity",
    "Harga Beli",
    "Diskon (%)",
    "Diskon (Rp)",
    "PPN (%)",
    "Status Pembayaran",
    "Memo",
    "No. Invoice",
    "Satuan",
  ];

  // Flatten data to simple rows
  const rows = purchasesData.flatMap((purchase) =>
    (purchase.items && purchase.items.length > 0 ? purchase.items : [{}]).map(
      (item: any) => [
        // Format date as YYYY-MM-DD for import compatibility
        purchase.purchaseDate
          ? new Date(purchase.purchaseDate).toISOString().split("T")[0]
          : "",
        purchase.supplier?.name || "",
        purchase.supplier?.phone || "",
        purchase.supplier?.email || "",
        item.product?.name || "",
        item.quantity || 0,
        item.costAtPurchase || 0,
        item.discountPercentage || 0,
        item.discountAmount || 0,
        // Extract PPN percentage from tax string (e.g., "11%" -> 11)
        item.tax ? parseFloat(item.tax.replace("%", "")) || 0 : 0,
        purchase.status === "LUNAS" ? "Lunas" : "Belum Lunas",
        purchase.memo || "",
        purchase.invoiceRef || "",
        item.unit || "Pcs",
      ]
    )
  );

  console.log(
    `[Export] Generated ${rows.length} rows for import-friendly format`
  );

  // Create worksheet with headers and data
  const worksheetData = [columns, ...rows];
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  worksheet["!cols"] = columns.map(() => ({ wch: 15 }));

  return worksheet;
};

export const createPurchasesExcelReport = (
  purchasesData: any[],
  reportPeriod: string,
  options: { companyName?: string; reportTitle?: string } = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const combinedSheet = createCombinedPurchasesSheet(
    purchasesData,
    reportPeriod
  );
  XLSX.utils.book_append_sheet(
    workbook,
    combinedSheet,
    "Laporan Rinci Pembelian"
  );
  return workbook;
};

export const createPurchasesImportFriendlyExcelReport = (
  purchasesData: any[]
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const worksheet = createPurchasesImportFriendlyWorksheet(purchasesData);
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data Pembelian");
  return workbook;
};
